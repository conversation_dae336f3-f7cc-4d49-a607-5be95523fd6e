#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能聊天机器人测试文件
用于测试各个模块的基本功能
"""

import sys
import os
from pathlib import Path

# 添加CustomFunction和Config目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "CustomFunction"))
sys.path.insert(0, str(Path(__file__).parent))

def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    try:
        from Config.Config import ConfigManager
        
        # 创建测试配置
        config = ConfigManager("test_config.json")
        
        # 测试基本功能
        config.set("test.key", "test_value")
        assert config.get("test.key") == "test_value"
        
        # 测试LLM配置
        config.set_llm_api_key("deepseek", "test_key")
        assert config.get("llm_models.deepseek.api_key") == "test_key"
        assert config.get("llm_models.deepseek.enabled") == True
        
        # 测试Agent配置
        agent_config = {
            "name": "测试Agent",
            "identity": "测试身份",
            "enabled": True
        }
        config.add_agent_config("test_agent", agent_config)
        assert config.get("agents.test_agent.name") == "测试Agent"
        
        print("✓ 配置管理器测试通过")
        
        # 清理测试文件
        if os.path.exists("test_config.json"):
            os.remove("test_config.json")
            
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_llm_manager():
    """测试LLM管理器"""
    print("=== 测试LLM管理器 ===")
    
    try:
        from CustomFunction.LLM import LLMManager, BaseLLM
        
        # 创建模拟LLM
        class MockLLM(BaseLLM):
            def __init__(self, name):
                super().__init__("mock_key", name)
                self.name = name
            
            def chat(self, messages, **kwargs):
                return f"Mock reply from {self.name}"
            
            def set_api_key(self, api_key):
                self.api_key = api_key
        
        # 测试LLM管理器
        manager = LLMManager()
        
        # 添加模型
        mock_llm1 = MockLLM("model1")
        mock_llm2 = MockLLM("model2")
        
        manager.add_model("model1", mock_llm1)
        manager.add_model("model2", mock_llm2)
        
        # 测试功能
        assert len(manager.list_models()) == 2
        assert manager.current_model == "model1"
        
        # 测试聊天
        messages = [{"role": "user", "content": "test"}]
        reply = manager.chat(messages)
        assert "Mock reply from model1" in reply
        
        # 切换模型
        manager.set_current_model("model2")
        reply = manager.chat(messages)
        assert "Mock reply from model2" in reply
        
        print("✓ LLM管理器测试通过")
        
    except Exception as e:
        print(f"✗ LLM管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_agent():
    """测试Agent"""
    print("=== 测试Agent ===")
    
    try:
        from CustomFunction.Agent import Agent, AgentManager
        from CustomFunction.LLM import BaseLLM
        
        # 创建模拟LLM
        class MockLLM(BaseLLM):
            def __init__(self):
                super().__init__("mock_key", "mock_model")
            
            def chat(self, messages, **kwargs):
                # 简单回复最后一条用户消息
                user_msg = ""
                for msg in messages:
                    if msg["role"] == "user":
                        user_msg = msg["content"]
                return f"回复: {user_msg}"
            
            def set_api_key(self, api_key):
                self.api_key = api_key
        
        # 测试Agent
        llm = MockLLM()
        agent = Agent(
            llm=llm,
            identity="测试助手",
            name="TestAgent"
        )
        
        # 测试生成回复
        reply = agent.generate_reply("你好")
        assert "回复: 你好" in reply
        
        # 测试历史记录
        assert len(agent.conversation_history) == 2  # user + assistant
        
        # 测试Agent管理器
        manager = AgentManager()
        manager.add_agent("test", agent)
        
        assert len(manager.list_agents()) == 1
        assert manager.get_agent("test") == agent
        
        reply = manager.generate_reply("测试消息", "test")
        assert "回复: 测试消息" in reply
        
        print("✓ Agent测试通过")
        
    except Exception as e:
        print(f"✗ Agent测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_integration():
    """集成测试"""
    print("=== 集成测试 ===")
    
    try:
        from Config.Config import ConfigManager
        from CustomFunction.LLM import create_llm_manager, BaseLLM
        from CustomFunction.Agent import create_agent_from_config
        
        # 创建模拟LLM类
        class MockDeepSeekLLM(BaseLLM):
            def __init__(self, api_key, model_name="mock-deepseek"):
                super().__init__(api_key, model_name)
            
            def chat(self, messages, **kwargs):
                return "DeepSeek模拟回复"
            
            def set_api_key(self, api_key):
                self.api_key = api_key
        
        # 临时替换真实的LLM类
        import CustomFunction.LLM as llm_module
        original_deepseek = llm_module.DeepSeekLLM
        llm_module.DeepSeekLLM = MockDeepSeekLLM
        
        try:
            # 创建配置
            config = ConfigManager("integration_test_config.json")
            config.set_llm_api_key("deepseek", "test_key")
            config.set_current_llm("deepseek")
            
            # 创建LLM管理器
            enabled_models = config.get_enabled_llm_models()
            llm_manager = create_llm_manager(enabled_models)
            
            # 创建Agent
            agent_config = {
                "name": "集成测试Agent",
                "identity": "测试助手",
                "llm_model": "deepseek",
                "enabled": True
            }
            agent = create_agent_from_config(agent_config, llm_manager)
            
            # 测试完整流程
            reply = agent.generate_reply("集成测试消息")
            assert "DeepSeek模拟回复" in reply
            
            print("✓ 集成测试通过")
            
        finally:
            # 恢复原始类
            llm_module.DeepSeekLLM = original_deepseek
            
            # 清理测试文件
            if os.path.exists("integration_test_config.json"):
                os.remove("integration_test_config.json")
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试所有模块是否可以正常导入
        from Config.Config import ConfigManager
        from CustomFunction.LLM import BaseLLM, DeepSeekLLM, ChatGPTLLM, QwenLLM, LLMManager
        from CustomFunction.Agent import Agent, AgentManager
        from CustomFunction.WeChatBot import WeChatBot
        
        print("✓ 所有模块导入成功")
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """运行所有测试"""
    print("微信智能聊天机器人测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_manager,
        test_llm_manager,
        test_agent,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"测试 {test.__name__} 出现异常: {e}")
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n接下来可以:")
        print("1. 配置真实的API Key")
        print("2. 运行 python main.py --setup 进行初始化")
        print("3. 运行 python main.py 启动机器人")
    else:
        print("❌ 部分测试失败，请检查代码")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
