{% extends "base.html" %}

{% block title %}Agent管理 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-robot text-primary"></i>
        Agent管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAgentModal">
            <i class="fas fa-plus"></i> 创建Agent
        </button>
    </div>
</div>

<!-- Agent列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i>
                    Agent配置列表
                </h6>
            </div>
            <div class="card-body">
                <div class="row" id="agents-container">
                    <!-- Agent卡片将通过JavaScript动态加载 -->
                </div>
                
                <!-- 空状态 -->
                <div id="empty-state" class="text-center py-5" style="display: none;">
                    <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无自定义Agent</h5>
                    <p class="text-muted">创建您的第一个自定义Agent来个性化聊天体验</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAgentModal">
                        <i class="fas fa-plus"></i> 创建Agent
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建Agent模态框 -->
<div class="modal fade" id="createAgentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i>
                    创建新Agent
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-agent-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="agent-name" class="form-label">Agent名称 *</label>
                                <input type="text" class="form-control" id="agent-name" 
                                       placeholder="例如：客服Agent" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="agent-identity" class="form-label">身份设定 *</label>
                                <input type="text" class="form-control" id="agent-identity" 
                                       placeholder="例如：专业的客服代表" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="agent-description" class="form-label">描述</label>
                        <textarea class="form-control" id="agent-description" rows="2" 
                                  placeholder="简要描述这个Agent的用途和特点"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="agent-prompt" class="form-label">系统提示词 *</label>
                        <textarea class="form-control" id="agent-prompt" rows="6" 
                                  placeholder="请输入详细的系统提示词，定义Agent的行为和回复风格..." required></textarea>
                        <div class="form-text">
                            系统提示词将决定Agent的回复风格和行为模式，请详细描述
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="agent-llm-model" class="form-label">选择模型 *</label>
                        <select class="form-select" id="agent-llm-model" required>
                            <option value="">请选择模型</option>
                        </select>
                        <div class="form-text">
                            只显示已验证成功的模型
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="agent-temperature" class="form-label">创造性 (Temperature)</label>
                                <input type="range" class="form-range" id="agent-temperature"
                                       min="0" max="1" step="0.1" value="0.7">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">保守 (0.0)</small>
                                    <small class="text-muted" id="temperature-value">0.7</small>
                                    <small class="text-muted">创新 (1.0)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="agent-max-tokens" class="form-label">最大回复长度</label>
                                <select class="form-select" id="agent-max-tokens">
                                    <option value="500">短回复 (500字符)</option>
                                    <option value="1000" selected>中等回复 (1000字符)</option>
                                    <option value="2000">长回复 (2000字符)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createAgent()">
                    <i class="fas fa-save"></i> 创建Agent
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑Agent模态框 -->
<div class="modal fade" id="editAgentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i>
                    编辑Agent
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="edit-agent-form">
                    <input type="hidden" id="edit-agent-id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-agent-name" class="form-label">Agent名称</label>
                                <input type="text" class="form-control" id="edit-agent-name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-agent-identity" class="form-label">身份设定</label>
                                <input type="text" class="form-control" id="edit-agent-identity">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit-agent-description" class="form-label">描述</label>
                        <textarea class="form-control" id="edit-agent-description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit-agent-prompt" class="form-label">系统提示词</label>
                        <textarea class="form-control" id="edit-agent-prompt" rows="6"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit-agent-llm-model" class="form-label">选择模型</label>
                        <select class="form-select" id="edit-agent-llm-model">
                            <option value="">请选择模型</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-agent-temperature" class="form-label">温度 (0.1-2.0)</label>
                                <input type="range" class="form-range" id="edit-agent-temperature"
                                       min="0.1" max="2.0" step="0.1" value="0.7">
                                <div class="text-muted small">当前值: <span id="edit-temperature-value">0.7</span></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit-agent-max-tokens" class="form-label">最大令牌数</label>
                                <input type="range" class="form-range" id="edit-agent-max-tokens"
                                       min="100" max="4000" step="100" value="1000">
                                <div class="text-muted small">当前值: <span id="edit-max-tokens-value">1000</span></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateAgent()">
                    <i class="fas fa-save"></i> 保存更改
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadEnabledModels();

        // 创建Agent模态框的温度滑块事件
        document.getElementById('agent-temperature').addEventListener('input', function() {
            document.getElementById('temperature-value').textContent = this.value;
        });

        // 创建Agent模态框的最大令牌数滑块事件
        document.getElementById('agent-max-tokens').addEventListener('input', function() {
            document.getElementById('max-tokens-value').textContent = this.value;
        });

        // 编辑Agent模态框的温度滑块事件
        document.getElementById('edit-agent-temperature').addEventListener('input', function() {
            document.getElementById('edit-temperature-value').textContent = this.value;
        });

        // 编辑Agent模态框的最大令牌数滑块事件
        document.getElementById('edit-agent-max-tokens').addEventListener('input', function() {
            document.getElementById('edit-max-tokens-value').textContent = this.value;
        });
    });

    // 加载可用的模型列表
    function loadEnabledModels() {
        fetch('/api/config/llm')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const createSelect = document.getElementById('agent-llm-model');
                    const editSelect = document.getElementById('edit-agent-llm-model');

                    // 清空选项
                    createSelect.innerHTML = '<option value="">请选择模型</option>';
                    editSelect.innerHTML = '<option value="">请选择模型</option>';

                    // 定义所有支持的模型
                    const allModels = {
                        'deepseek': ['deepseek-chat', 'deepseek-reasoner', 'deepseek-coder'],
                        'chatgpt': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
                        'qwen': ['qwen-turbo', 'qwen-plus', 'qwen-max']
                    };

                    // 添加所有模型选项
                    Object.keys(allModels).forEach(providerName => {
                        const config = data.data[providerName] || {};
                        const isEnabled = config.enabled && config.api_key;

                        allModels[providerName].forEach(modelName => {
                            const displayName = getModelDisplayName(providerName, modelName);
                            const optionText = isEnabled ? displayName : `${displayName} (需要配置API Key)`;

                            const option = new Option(optionText, modelName);
                            if (!isEnabled) {
                                option.disabled = true;
                                option.style.color = '#6c757d';
                            }

                            createSelect.add(option.cloneNode(true));
                            editSelect.add(option);
                        });
                    });
                }
            })
            .catch(error => {
                console.error('加载模型列表失败:', error);
            });
    }

    // 获取默认模型
    function getDefaultModel(providerName) {
        const defaultModels = {
            'deepseek': 'deepseek-chat',
            'chatgpt': 'gpt-3.5-turbo',
            'qwen': 'qwen-turbo'
        };
        return defaultModels[providerName] || providerName;
    }

    // 获取模型显示名称
    function getModelDisplayName(providerName, modelName) {
        const modelDisplayNames = {
            'deepseek-chat': 'DeepSeek Chat (通用对话)',
            'deepseek-reasoner': 'DeepSeek Reasoner (推理模型)',
            'deepseek-coder': 'DeepSeek Coder (代码专用)',
            'gpt-3.5-turbo': 'GPT-3.5 Turbo',
            'gpt-4': 'GPT-4',
            'gpt-4-turbo': 'GPT-4 Turbo',
            'gpt-4o': 'GPT-4o',
            'qwen-turbo': 'Qwen Turbo',
            'qwen-plus': 'Qwen Plus',
            'qwen-max': 'Qwen Max',
            'qwen-long': 'Qwen Long'
        };
        return modelDisplayNames[modelName] || modelName;
    }

    // 加载Agent列表
    function loadAgents() {
        fetch('/api/agents')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('Agent列表加载成功:', data.data);
                    renderAgentCards(data.data);
                } else {
                    console.error('加载Agent列表失败:', data.message);
                }
            })
            .catch(error => {
                console.error('加载Agent列表失败:', error);
            });
    }

    // 渲染Agent卡片
    function renderAgentCards(agents) {
        const container = document.getElementById('agents-container');

        // 清空所有现有卡片
        container.innerHTML = '';

        // 分离系统Agent和用户Agent
        const systemAgents = ['default', 'friend', 'work'];
        const systemAgentData = {};
        const userAgentData = {};

        Object.keys(agents).forEach(agentId => {
            if (systemAgents.includes(agentId)) {
                systemAgentData[agentId] = agents[agentId];
            } else {
                userAgentData[agentId] = agents[agentId];
            }
        });

        // 先渲染系统Agent（按固定顺序）
        systemAgents.forEach(agentId => {
            if (systemAgentData[agentId]) {
                const agent = systemAgentData[agentId];
                const cardHtml = createAgentCardHtml(agentId, agent);
                container.insertAdjacentHTML('beforeend', cardHtml);
            }
        });

        // 再渲染用户创建的Agent
        Object.keys(userAgentData).forEach(agentId => {
            const agent = userAgentData[agentId];
            const cardHtml = createAgentCardHtml(agentId, agent);
            container.insertAdjacentHTML('beforeend', cardHtml);
        });
    }

    // 创建Agent卡片HTML
    function createAgentCardHtml(agentId, agent) {
        // 根据Agent类型设置不同样式
        let cardClass, headerClass, icon, badge;

        if (agentId === 'default') {
            cardClass = 'border-primary';
            headerClass = 'bg-primary';
            icon = 'fas fa-robot';
            badge = '<span class="badge bg-light text-primary ms-2">默认</span>';
        } else if (agentId === 'friend') {
            cardClass = 'border-success';
            headerClass = 'bg-success';
            icon = 'fas fa-heart';
            badge = '';
        } else if (agentId === 'work') {
            cardClass = 'border-info';
            headerClass = 'bg-info';
            icon = 'fas fa-briefcase';
            badge = '';
        } else {
            cardClass = 'border-secondary';
            headerClass = 'bg-secondary';
            icon = 'fas fa-user-robot';
            badge = agent.enabled ?
                '<span class="badge bg-success ms-2">启用</span>' :
                '<span class="badge bg-secondary ms-2">禁用</span>';
        }

        return `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card ${cardClass}">
                    <div class="card-header ${headerClass} text-white">
                        <h6 class="mb-0">
                            <i class="${icon}"></i>
                            ${agent.name || '未命名Agent'}
                            ${badge}
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="card-text text-muted">${agent.description || '暂无描述'}</p>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-user"></i>
                                身份: ${agent.identity || '未设置'}
                            </small>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">
                                <i class="fas fa-brain"></i>
                                模型: ${getModelDisplayName('', agent.llm_model) || '未设置'}
                            </small>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-thermometer-half"></i>
                                温度: ${agent.temperature || 0.7}
                            </small>
                        </div>
                        <div class="d-grid gap-2 ${['default', 'friend', 'work'].includes(agentId) ? '' : 'd-md-flex'}">
                            <button class="btn btn-outline-primary btn-sm ${['default', 'friend', 'work'].includes(agentId) ? '' : 'flex-fill'}" onclick="editAgent('${agentId}')">
                                <i class="fas fa-edit"></i>
                                编辑
                            </button>
                            ${['default', 'friend', 'work'].includes(agentId) ? '' : `
                            <button class="btn btn-outline-danger btn-sm" onclick="deleteAgent('${agentId}')">
                                <i class="fas fa-trash"></i>
                            </button>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 创建Agent
    function createAgent() {
        const name = document.getElementById('agent-name').value.trim();
        const identity = document.getElementById('agent-identity').value.trim();
        const description = document.getElementById('agent-description').value.trim();
        const prompt = document.getElementById('agent-prompt').value.trim();
        const llmModel = document.getElementById('agent-llm-model').value;
        const temperature = parseFloat(document.getElementById('agent-temperature').value);
        const maxTokens = parseInt(document.getElementById('agent-max-tokens').value);

        if (!name || !identity || !prompt || !llmModel) {
            showNotification('请填写所有必填字段', 'warning');
            return;
        }

        const agentData = {
            name: name,
            identity: identity,
            description: description,
            prompt: prompt,
            llm_model: llmModel,
            temperature: temperature,
            max_tokens: maxTokens
        };

        // 调用API创建Agent
        fetch('/api/agents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(agentData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Agent创建成功', 'success');
                loadAgents(); // 重新加载Agent列表

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('createAgentModal'));
                modal.hide();

                // 清空表单
                document.getElementById('create-agent-form').reset();
                document.getElementById('temperature-value').textContent = '0.7';
            } else {
                showNotification('创建失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('创建Agent失败:', error);
            showNotification('创建失败: ' + error.message, 'error');
        });
    }

    // 删除Agent
    function deleteAgent(agentId) {
        // 检查是否为系统保护的Agent
        const protectedAgents = ['default', 'friend', 'work'];
        if (protectedAgents.includes(agentId)) {
            showNotification('不能删除系统内置Agent模板', 'warning');
            return;
        }

        if (!confirm('确定要删除这个Agent吗？此操作不可撤销。')) {
            return;
        }

        fetch(`/api/agents/${agentId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Agent删除成功', 'success');
                loadAgents(); // 重新加载Agent列表
            } else {
                showNotification('删除失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('删除Agent失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        });
    }

    // 编辑Agent
    function editAgent(agentId) {
        console.log('编辑Agent:', agentId);

        // 从API获取Agent详情
        fetch(`/api/agents/${agentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const agent = data.data;
                    document.getElementById('edit-agent-id').value = agentId;
                    document.getElementById('edit-agent-name').value = agent.name;
                    document.getElementById('edit-agent-identity').value = agent.identity;
                    document.getElementById('edit-agent-description').value = agent.description || '';
                    document.getElementById('edit-agent-prompt').value = agent.prompt;
                    document.getElementById('edit-agent-llm-model').value = agent.llm_model;
                    document.getElementById('edit-agent-temperature').value = agent.temperature || 0.7;
                    document.getElementById('edit-agent-max-tokens').value = agent.max_tokens || 1000;

                    // 更新滑块显示值
                    document.getElementById('edit-temperature-value').textContent = agent.temperature || 0.7;
                    document.getElementById('edit-max-tokens-value').textContent = agent.max_tokens || 1000;

                    const modal = new bootstrap.Modal(document.getElementById('editAgentModal'));
                    modal.show();
                } else {
                    showNotification('获取Agent详情失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('获取Agent详情失败:', error);
                showNotification('获取Agent详情失败: ' + error.message, 'error');
            });
    }
    
    // 更新Agent
    function updateAgent() {
        const agentId = document.getElementById('edit-agent-id').value;
        const name = document.getElementById('edit-agent-name').value.trim();
        const identity = document.getElementById('edit-agent-identity').value.trim();
        const description = document.getElementById('edit-agent-description').value.trim();
        const prompt = document.getElementById('edit-agent-prompt').value.trim();
        const llmModel = document.getElementById('edit-agent-llm-model').value;
        const temperature = parseFloat(document.getElementById('edit-agent-temperature').value);
        const maxTokens = parseInt(document.getElementById('edit-agent-max-tokens').value);

        if (!name || !identity || !prompt) {
            showNotification('请填写必填字段', 'warning');
            return;
        }

        const agentData = {
            name: name,
            identity: identity,
            description: description,
            prompt: prompt,
            llm_model: llmModel,
            temperature: temperature,
            max_tokens: maxTokens
        };

        // 调用API更新Agent
        fetch(`/api/agents/${agentId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(agentData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Agent更新成功', 'success');
                loadAgents(); // 重新加载Agent列表

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('editAgentModal'));
                modal.hide();
            } else {
                showNotification('更新失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('更新Agent失败:', error);
            showNotification('更新失败: ' + error.message, 'error');
        });
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadEnabledModels(); // 加载可用模型列表
        loadAgents(); // 加载Agent列表
    });
</script>
{% endblock %}
