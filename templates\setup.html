{% extends "base.html" %}

{% block title %}系统配置 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog text-primary"></i>
        系统配置
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-success" onclick="saveAllConfig()">
            <i class="fas fa-save"></i> 保存配置
        </button>
    </div>
</div>

<!-- LLM模型配置 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-brain"></i>
                    LLM模型配置
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- DeepSeek配置 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-robot"></i>
                                    DeepSeek
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="deepseek-api-key" class="form-label">API Key</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="deepseek-api-key" 
                                               placeholder="sk-xxxxxxxxxxxxxxxx">
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="togglePassword('deepseek-api-key')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="deepseek-model" class="form-label">模型选择</label>
                                    <select class="form-select" id="deepseek-model">
                                        <option value="deepseek-chat">deepseek-chat (通用对话模型)</option>
                                        <option value="deepseek-reasoner">deepseek-reasoner (推理模型)</option>
                                        <option value="deepseek-coder">deepseek-coder (代码专用模型)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="deepseek-enabled">
                                        <label class="form-check-label" for="deepseek-enabled">
                                            启用此模型
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="validateApiKey('deepseek')">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="deepseek-status">验证API Key</span>
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        获取API Key: <a href="https://platform.deepseek.com" target="_blank">DeepSeek平台</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- ChatGPT配置 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-comments"></i>
                                    ChatGPT
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="chatgpt-api-key" class="form-label">API Key</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="chatgpt-api-key" 
                                               placeholder="sk-xxxxxxxxxxxxxxxx">
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="togglePassword('chatgpt-api-key')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="chatgpt-model" class="form-label">模型名称</label>
                                    <select class="form-select" id="chatgpt-model">
                                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                        <option value="gpt-4">GPT-4</option>
                                        <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="chatgpt-enabled">
                                        <label class="form-check-label" for="chatgpt-enabled">
                                            启用此模型
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="validateApiKey('chatgpt')">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="chatgpt-status">验证API Key</span>
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        获取API Key: <a href="https://platform.openai.com" target="_blank">OpenAI平台</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Qwen配置 -->
                    <div class="col-md-4 mb-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-cloud"></i>
                                    Qwen
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="qwen-api-key" class="form-label">API Key</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="qwen-api-key" 
                                               placeholder="sk-xxxxxxxxxxxxxxxx">
                                        <button class="btn btn-outline-secondary" type="button" 
                                                onclick="togglePassword('qwen-api-key')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="qwen-model" class="form-label">模型名称</label>
                                    <select class="form-select" id="qwen-model">
                                        <option value="qwen-turbo">Qwen Turbo</option>
                                        <option value="qwen-plus">Qwen Plus</option>
                                        <option value="qwen-max">Qwen Max</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="qwen-enabled">
                                        <label class="form-check-label" for="qwen-enabled">
                                            启用此模型
                                        </label>
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-info" onclick="validateApiKey('qwen')">
                                        <i class="fas fa-check-circle"></i>
                                        <span id="qwen-status">验证API Key</span>
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        获取API Key: <a href="https://dashscope.aliyun.com" target="_blank">阿里云平台</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通用设置 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-sliders-h"></i>
                    通用设置
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="current-llm" class="form-label">默认LLM模型</label>
                    <select class="form-select" id="current-llm">
                        <option value="">请选择模型</option>
                        <option value="deepseek">DeepSeek</option>
                        <option value="chatgpt">ChatGPT</option>
                        <option value="qwen">Qwen</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="reply-delay" class="form-label">回复延迟 (秒)</label>
                    <input type="number" class="form-control" id="reply-delay" 
                           value="1.0" min="0.5" max="10" step="0.5">
                    <div class="form-text">设置自动回复的延迟时间，避免回复过快</div>
                </div>
                <div class="mb-3">
                    <label for="max-history" class="form-label">最大对话历史</label>
                    <input type="number" class="form-control" id="max-history" 
                           value="10" min="5" max="50">
                    <div class="form-text">保留的对话历史条数，影响AI的记忆能力</div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="debug-mode">
                        <label class="form-check-label" for="debug-mode">
                            调试模式
                        </label>
                    </div>
                    <div class="form-text">启用后会输出详细的调试信息</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-shield-alt"></i>
                    安全设置
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <button class="btn btn-outline-primary w-100" onclick="exportConfig()">
                        <i class="fas fa-download"></i>
                        导出配置文件
                    </button>
                </div>
                <div class="mb-3">
                    <label for="config-file" class="form-label">导入配置文件</label>
                    <input type="file" class="form-control" id="config-file" accept=".json" 
                           onchange="importConfig(this)">
                </div>
                <div class="mb-3">
                    <button class="btn btn-outline-warning w-100" onclick="resetConfig()">
                        <i class="fas fa-undo"></i>
                        重置为默认配置
                    </button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-outline-danger w-100" onclick="clearApiKeys()">
                        <i class="fas fa-trash"></i>
                        清空所有API密钥
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 配置状态 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-line"></i>
                    配置状态
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-robot fa-2x" id="deepseek-icon"></i>
                        </div>
                        <h6>DeepSeek</h6>
                        <span class="badge bg-secondary" id="deepseek-badge">未配置</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-comments fa-2x" id="chatgpt-icon"></i>
                        </div>
                        <h6>ChatGPT</h6>
                        <span class="badge bg-secondary" id="chatgpt-badge">未配置</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-cloud fa-2x" id="qwen-icon"></i>
                        </div>
                        <h6>Qwen</h6>
                        <span class="badge bg-secondary" id="qwen-badge">未配置</span>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x text-success" id="overall-icon"></i>
                        </div>
                        <h6>整体状态</h6>
                        <span class="badge bg-warning" id="overall-badge">需要配置</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时获取当前配置
    document.addEventListener('DOMContentLoaded', function() {
        loadCurrentConfig();
    });
    
    // 加载当前配置
    function loadCurrentConfig() {
        fetch('/api/config/llm/edit')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const config = data.data;
                    
                    // 填充表单
                    Object.keys(config).forEach(model => {
                        const modelConfig = config[model];
                        const apiKeyInput = document.getElementById(`${model}-api-key`);
                        const enabledInput = document.getElementById(`${model}-enabled`);
                        const modelSelect = document.getElementById(`${model}-model`);

                        if (apiKeyInput) {
                            // 始终填充完整的API密钥（从编辑端点获取的是完整密钥）
                            if (modelConfig.api_key) {
                                apiKeyInput.value = modelConfig.api_key;
                            }
                        }
                        if (enabledInput) {
                            enabledInput.checked = modelConfig.enabled || false;
                        }
                        if (modelSelect && modelConfig.model_name) {
                            modelSelect.value = modelConfig.model_name;
                        }

                        // 更新状态显示
                        updateModelStatus(model, modelConfig.enabled && modelConfig.api_key);
                    });
                    
                    updateOverallStatus();
                }
            })
            .catch(error => {
                console.error('加载配置失败:', error);
                showNotification('加载配置失败', 'error');
            });
    }
    
    // 切换密码显示
    function togglePassword(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.nextElementSibling.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
    
    // 验证API密钥
    function validateApiKey(model) {
        const apiKey = document.getElementById(`${model}-api-key`).value;
        const modelName = document.getElementById(`${model}-model`).value;
        const statusSpan = document.getElementById(`${model}-status`);
        const button = statusSpan.parentElement;

        if (!apiKey) {
            showNotification('请先输入API密钥', 'warning');
            return;
        }

        // 显示验证中状态
        button.disabled = true;
        statusSpan.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';

        fetch('/api/config/validate-key', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                provider: model,
                api_key: apiKey,
                model_name: modelName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                statusSpan.innerHTML = '<i class="fas fa-check"></i> 验证成功';
                button.className = button.className.replace(/btn-\w+/, 'btn-success');
                updateModelStatus(model, true);
                showNotification(`${model.toUpperCase()} API密钥验证成功并已保存`, 'success');

                // 自动启用模型
                const enabledCheckbox = document.getElementById(`${model}-enabled`);
                if (enabledCheckbox) {
                    enabledCheckbox.checked = true;
                }
            } else {
                statusSpan.innerHTML = '<i class="fas fa-times"></i> 验证失败';
                button.className = button.className.replace(/btn-\w+/, 'btn-danger');
                updateModelStatus(model, false);
                showNotification(`${model.toUpperCase()} API密钥验证失败: ${data.message}`, 'error');
            }
        })
        .catch(error => {
            console.error('验证失败:', error);
            statusSpan.innerHTML = '<i class="fas fa-times"></i> 验证失败';
            button.className = button.className.replace(/btn-\w+/, 'btn-danger');
            updateModelStatus(model, false);
            showNotification('验证请求失败', 'error');
        })
        .finally(() => {
            button.disabled = false;
            setTimeout(() => {
                statusSpan.textContent = '验证API Key';
                button.className = button.className.replace(/btn-(success|danger)/, 'btn-primary');
            }, 3000);
        });
    }
    
    // 更新模型状态显示
    function updateModelStatus(model, isValid) {
        const badge = document.getElementById(`${model}-badge`);
        const icon = document.getElementById(`${model}-icon`);
        
        if (isValid) {
            badge.className = 'badge bg-success';
            badge.textContent = '已配置';
            icon.className = icon.className.replace('text-muted', 'text-success');
        } else {
            badge.className = 'badge bg-secondary';
            badge.textContent = '未配置';
            icon.className = icon.className.replace('text-success', 'text-muted');
        }
        
        updateOverallStatus();
    }
    
    // 更新整体状态
    function updateOverallStatus() {
        const badges = ['deepseek-badge', 'chatgpt-badge', 'qwen-badge'];
        const configuredCount = badges.filter(id => 
            document.getElementById(id).textContent === '已配置'
        ).length;
        
        const overallBadge = document.getElementById('overall-badge');
        const overallIcon = document.getElementById('overall-icon');
        
        if (configuredCount === 0) {
            overallBadge.className = 'badge bg-danger';
            overallBadge.textContent = '需要配置';
            overallIcon.className = 'fas fa-exclamation-circle fa-2x text-danger';
        } else if (configuredCount < 3) {
            overallBadge.className = 'badge bg-warning';
            overallBadge.textContent = '部分配置';
            overallIcon.className = 'fas fa-exclamation-triangle fa-2x text-warning';
        } else {
            overallBadge.className = 'badge bg-success';
            overallBadge.textContent = '配置完成';
            overallIcon.className = 'fas fa-check-circle fa-2x text-success';
        }
    }
    
    // 保存所有配置
    function saveAllConfig() {
        const models = ['deepseek', 'chatgpt', 'qwen'];
        const promises = [];
        
        models.forEach(model => {
            const apiKey = document.getElementById(`${model}-api-key`).value;
            const enabled = document.getElementById(`${model}-enabled`).checked;
            const modelSelect = document.getElementById(`${model}-model`);
            const selectedModel = modelSelect ? modelSelect.value : '';

            if (apiKey) {
                promises.push(
                    fetch('/api/config/llm', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            model_name: model,
                            api_key: apiKey,
                            enabled: enabled,
                            selected_model: selectedModel  // 添加具体的模型选择
                        })
                    })
                );
            }
        });
        
        Promise.all(promises)
            .then(responses => Promise.all(responses.map(r => r.json())))
            .then(results => {
                const allSuccess = results.every(r => r.success);
                if (allSuccess) {
                    showNotification('配置保存成功', 'success');
                } else {
                    showNotification('部分配置保存失败', 'warning');
                }
            })
            .catch(error => {
                console.error('保存配置失败:', error);
                showNotification('保存配置失败', 'error');
            });
    }
    
    // 导出配置
    function exportConfig() {
        fetch('/api/config/llm')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const configBlob = new Blob([JSON.stringify(data.data, null, 2)], {
                        type: 'application/json'
                    });
                    const url = URL.createObjectURL(configBlob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'wxbot_config.json';
                    a.click();
                    URL.revokeObjectURL(url);
                    showNotification('配置文件已导出', 'success');
                }
            })
            .catch(error => {
                console.error('导出失败:', error);
                showNotification('导出配置失败', 'error');
            });
    }
    
    // 导入配置
    function importConfig(input) {
        const file = input.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const config = JSON.parse(e.target.result);
                
                // 填充表单
                Object.keys(config).forEach(model => {
                    if (document.getElementById(`${model}-api-key`)) {
                        document.getElementById(`${model}-api-key`).value = config[model].api_key || '';
                        document.getElementById(`${model}-enabled`).checked = config[model].enabled || false;
                    }
                });
                
                showNotification('配置文件导入成功', 'success');
            } catch (error) {
                console.error('解析配置文件失败:', error);
                showNotification('配置文件格式错误', 'error');
            }
        };
        reader.readAsText(file);
    }
    
    // 重置配置
    function resetConfig() {
        if (confirm('确定要重置为默认配置吗？这将清空所有当前设置。')) {
            const models = ['deepseek', 'chatgpt', 'qwen'];
            models.forEach(model => {
                document.getElementById(`${model}-api-key`).value = '';
                document.getElementById(`${model}-enabled`).checked = false;
                updateModelStatus(model, false);
            });
            showNotification('配置已重置', 'info');
        }
    }
    
    // 清空API密钥
    function clearApiKeys() {
        if (confirm('确定要清空所有API密钥吗？')) {
            const models = ['deepseek', 'chatgpt', 'qwen'];
            models.forEach(model => {
                document.getElementById(`${model}-api-key`).value = '';
                updateModelStatus(model, false);
            });
            showNotification('API密钥已清空', 'info');
        }
    }
</script>
{% endblock %}
