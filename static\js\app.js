// 微信智能机器人 - 前端JavaScript

// 全局变量
let socket;
let notificationContainer;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 初始化Socket.IO连接
    initializeSocket();
    
    // 创建通知容器
    createNotificationContainer();
    
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化移动端侧边栏
    initializeMobileSidebar();
    
    // 设置当前页面导航高亮
    setActiveNavigation();
    
    console.log('微信智能机器人前端已初始化');
}

// 初始化Socket.IO连接
function initializeSocket() {
    socket = io();
    
    // 连接成功
    socket.on('connect', function() {
        console.log('WebSocket连接已建立');
        showNotification('已连接到服务器', 'success', 3000);
    });
    
    // 连接断开
    socket.on('disconnect', function() {
        console.log('WebSocket连接已断开');
        showNotification('与服务器连接断开', 'warning', 5000);
    });
    
    // 机器人状态变更
    socket.on('bot_status_changed', function(data) {
        console.log('机器人状态变更:', data);
        if (typeof updateBotStatus === 'function') {
            updateBotStatus(data);
        }
    });
    
    // 新消息通知
    socket.on('new_message', function(data) {
        console.log('收到新消息:', data);
        showNotification(`收到来自 ${data.sender} 的消息`, 'info', 5000);
        if (typeof handleNewMessage === 'function') {
            handleNewMessage(data);
        }
    });
    
    // 系统通知
    socket.on('system_notification', function(data) {
        console.log('系统通知:', data);
        showNotification(data.message, data.type || 'info', data.duration || 5000);
    });
    
    // 错误处理
    socket.on('error', function(error) {
        console.error('WebSocket错误:', error);
        showNotification('连接错误，请刷新页面重试', 'error', 10000);
    });
}

// 创建通知容器
function createNotificationContainer() {
    notificationContainer = document.createElement('div');
    notificationContainer.className = 'notification';
    notificationContainer.id = 'notification-container';
    document.body.appendChild(notificationContainer);
}

// 显示通知
function showNotification(message, type = 'info', duration = 5000) {
    if (!notificationContainer) {
        createNotificationContainer();
    }
    
    const alertTypes = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };
    
    const icons = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'warning': 'fas fa-exclamation-triangle',
        'info': 'fas fa-info-circle'
    };
    
    const alertClass = alertTypes[type] || 'alert-info';
    const iconClass = icons[type] || 'fas fa-info-circle';
    
    const alertElement = document.createElement('div');
    alertElement.className = `alert ${alertClass} alert-dismissible fade show`;
    alertElement.innerHTML = `
        <i class="${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    notificationContainer.appendChild(alertElement);
    
    // 自动消失
    if (duration > 0) {
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.classList.remove('show');
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.parentNode.removeChild(alertElement);
                    }
                }, 150);
            }
        }, duration);
    }
}

// 初始化工具提示
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化移动端侧边栏
function initializeMobileSidebar() {
    // 创建移动端菜单按钮
    const navbar = document.querySelector('.navbar');
    if (navbar && window.innerWidth <= 768) {
        const menuButton = document.createElement('button');
        menuButton.className = 'btn btn-primary d-md-none';
        menuButton.innerHTML = '<i class="fas fa-bars"></i>';
        menuButton.onclick = toggleSidebar;
        navbar.appendChild(menuButton);
    }
    
    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        const sidebar = document.querySelector('.sidebar');
        if (window.innerWidth > 768 && sidebar) {
            sidebar.classList.remove('show');
        }
    });
}

// 切换侧边栏显示
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}

// 设置当前页面导航高亮
function setActiveNavigation() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// API请求封装
class ApiClient {
    static async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    static async get(url) {
        return this.request(url, { method: 'GET' });
    }
    
    static async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }
    
    static async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }
    
    static async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }
}

// 工具函数
const Utils = {
    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN');
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            showNotification('已复制到剪贴板', 'success', 2000);
        } catch (err) {
            console.error('复制失败:', err);
            showNotification('复制失败', 'error', 3000);
        }
    },
    
    // 下载文件
    downloadFile(content, filename, contentType = 'text/plain') {
        const blob = new Blob([content], { type: contentType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
    },
    
    // 验证表单
    validateForm(formElement) {
        const inputs = formElement.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        return isValid;
    }
};

// 页面特定功能
const PageHandlers = {
    // 控制面板页面
    dashboard: {
        init() {
            console.log('控制面板页面已初始化');
        },
        
        updateStatus(data) {
            // 更新机器人状态显示
            console.log('更新控制面板状态:', data);
        }
    },
    
    // 配置页面
    setup: {
        init() {
            console.log('配置页面已初始化');
        },
        
        validateApiKey(model, apiKey) {
            return ApiClient.post('/api/config/validate-key', {
                model_name: model,
                api_key: apiKey
            });
        }
    },
    
    // 好友管理页面
    friends: {
        init() {
            console.log('好友管理页面已初始化');
        },
        
        loadFriends() {
            return ApiClient.get('/api/friends');
        },
        
        addFriend(friendData) {
            return ApiClient.post('/api/friends', friendData);
        },
        
        removeFriend(friendName) {
            return ApiClient.delete(`/api/friends/${encodeURIComponent(friendName)}`);
        }
    }
};

// 根据当前页面初始化对应的处理器
function initializePageHandler() {
    const path = window.location.pathname;
    
    if (path.includes('dashboard')) {
        PageHandlers.dashboard.init();
    } else if (path.includes('setup')) {
        PageHandlers.setup.init();
    } else if (path.includes('friends')) {
        PageHandlers.friends.init();
    }
}

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    showNotification('发生未知错误，请刷新页面重试', 'error', 10000);
});

// 全局Promise错误处理
window.addEventListener('unhandledrejection', function(event) {
    console.error('未处理的Promise错误:', event.reason);
    showNotification('请求失败，请检查网络连接', 'error', 5000);
});

// 导出全局对象
window.WxBot = {
    ApiClient,
    Utils,
    PageHandlers,
    showNotification,
    socket: () => socket
};

// 页面加载完成后初始化页面处理器
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializePageHandler, 100);
});
