import json
import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path


class ConfigManager:
    """配置管理器，用于管理应用程序配置"""
    
    def __init__(self, config_file: str = "Config/config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.logs_file = Path(config_file).parent / "logs.json"
        self.config = {}
        self.logs = []
        self.load_config()
        self.load_logs()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                # 创建默认配置
                self.config = self._create_default_config()
                self.save_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.config = self._create_default_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "llm_models": {
                "deepseek": {
                    "api_key": "",
                    "model_name": "deepseek-chat",
                    "enabled": False
                },
                "chatgpt": {
                    "api_key": "",
                    "model_name": "gpt-3.5-turbo",
                    "enabled": False
                },
                "qwen": {
                    "api_key": "",
                    "model_name": "qwen-turbo",
                    "enabled": False
                }
            },
            "agents": {
                "default": {
                    "name": "默认回复助手",
                    "identity": "用户本人",
                    "llm_model": "deepseek",
                    "prompt": "你需要模仿用户的说话习惯和语气来回复消息。请用自然、真实的语气回复，就像用户本人在回复一样。回复要简洁、符合用户的性格特点。",
                    "enabled": True
                },
                "friend": {
                    "name": "朋友Agent",
                    "identity": "用户本人（朋友关系）",
                    "llm_model": "deepseek",
                    "prompt": "你正在帮助用户回复朋友的消息。请模仿用户与朋友聊天时的语气和习惯：轻松、友好、幽默、亲切。回复要自然随意，可以使用表情符号，就像用户本人在和朋友聊天一样。避免过于正式或商务化的语言。",
                    "enabled": True
                },
                "work": {
                    "name": "销售Agent",
                    "identity": "专业销售人员",
                    "llm_model": "deepseek",
                    "prompt": "你是一个专业的销售人员，负责向顾客推荐产品和解决问题。你需要：1）友好专业地回复顾客咨询；2）根据顾客需求推荐合适的产品；3）解答产品相关问题；4）维护良好的客户关系。回复要热情、专业、有说服力，但不要过于推销。",
                    "enabled": True
                }
            },
            "wechat": {
                "listen_friends": [],  # 要监听的好友列表
                "auto_reply": True,    # 是否自动回复
                "reply_delay": 1.0,    # 回复延迟（秒）
                "debug": False         # 调试模式
            },
            "general": {
                "current_llm": "deepseek",
                "current_agent": "default",
                "max_history_length": 10
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self.save_config()
    
    def get_llm_config(self, model_name: str = None) -> Dict[str, Any]:
        """获取LLM配置"""
        if model_name:
            return self.get(f"llm_models.{model_name}", {})
        else:
            return self.get("llm_models", {})
    
    def set_llm_api_key(self, model_name: str, api_key: str):
        """设置LLM API密钥"""
        self.set(f"llm_models.{model_name}.api_key", api_key)
        self.set(f"llm_models.{model_name}.enabled", bool(api_key))

    def set_llm_enabled(self, model_name: str, enabled: bool):
        """设置LLM模型启用状态"""
        self.set(f"llm_models.{model_name}.enabled", enabled)

    def set_llm_model_name(self, model_name: str, model_variant: str):
        """设置LLM模型的具体模型名称"""
        self.set(f"llm_models.{model_name}.model_name", model_variant)

    def get_available_models(self, provider: str) -> list:
        """获取指定提供商的可用模型列表"""
        model_options = {
            "deepseek": [
                "deepseek-chat",      # DeepSeek-V3-0324 通用对话模型
                "deepseek-reasoner",  # DeepSeek-R1-0528 推理模型
                "deepseek-coder"      # 代码专用模型（保留兼容性）
            ],
            "chatgpt": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"],
            "qwen": ["qwen-turbo", "qwen-plus", "qwen-max", "qwen-long"]
        }
        return model_options.get(provider, [])
    
    def get_agent_config(self, agent_name: str = None) -> Dict[str, Any]:
        """获取Agent配置"""
        if agent_name:
            return self.get(f"agents.{agent_name}", {})
        else:
            return self.get("agents", {})
    
    def add_agent_config(self, agent_name: str, config: Dict[str, Any]):
        """添加Agent配置"""
        self.set(f"agents.{agent_name}", config)
    
    def remove_agent_config(self, agent_name: str):
        """移除Agent配置"""
        agents = self.get("agents", {})
        if agent_name in agents:
            del agents[agent_name]
            self.set("agents", agents)
    
    def get_wechat_config(self) -> Dict[str, Any]:
        """获取微信配置"""
        return self.get("wechat", {})
    
    def add_listen_friend(self, friend_name: str, agent: str = None, tags: list = None, auto_reply: bool = True):
        """添加监听好友"""
        friends = self.get("wechat.listen_friends", [])

        # 检查是否已存在
        existing_friend = None
        for i, friend in enumerate(friends):
            if isinstance(friend, dict) and friend.get('name') == friend_name:
                existing_friend = i
                break
            elif isinstance(friend, str) and friend == friend_name:
                existing_friend = i
                break

        # 创建好友配置
        friend_config = {
            'name': friend_name,
            'agent': agent or 'default',
            'tags': tags or ['好友'],
            'auto_reply': auto_reply,
            'last_active': None,
            'message_count': 0
        }

        if existing_friend is not None:
            friends[existing_friend] = friend_config
        else:
            friends.append(friend_config)

        self.set("wechat.listen_friends", friends)

    def remove_listen_friend(self, friend_name: str):
        """移除监听好友"""
        friends = self.get("wechat.listen_friends", [])
        updated_friends = []

        for friend in friends:
            if isinstance(friend, dict):
                if friend.get('name') != friend_name:
                    updated_friends.append(friend)
            elif isinstance(friend, str):
                if friend != friend_name:
                    updated_friends.append(friend)

        self.set("wechat.listen_friends", updated_friends)

    def get_listen_friends(self) -> list:
        """获取监听好友列表"""
        friends = self.get("wechat.listen_friends", [])
        # 确保返回统一格式
        normalized_friends = []
        for friend in friends:
            if isinstance(friend, dict):
                normalized_friends.append(friend)
            elif isinstance(friend, str):
                # 转换旧格式
                normalized_friends.append({
                    'name': friend,
                    'agent': 'default',
                    'tags': ['好友'],
                    'auto_reply': True,
                    'last_active': None,
                    'message_count': 0
                })
        return normalized_friends

    def update_friend_config(self, friend_name: str, agent: str = None, tags: list = None, auto_reply: bool = None):
        """更新好友配置"""
        friends = self.get("wechat.listen_friends", [])

        for i, friend in enumerate(friends):
            friend_dict = friend if isinstance(friend, dict) else {'name': friend}
            if friend_dict.get('name') == friend_name:
                if agent is not None:
                    friend_dict['agent'] = agent
                if tags is not None:
                    friend_dict['tags'] = tags
                if auto_reply is not None:
                    friend_dict['auto_reply'] = auto_reply
                friends[i] = friend_dict
                break

        self.set("wechat.listen_friends", friends)

    def get_friend_config(self, friend_name: str) -> dict:
        """获取特定好友的配置"""
        friends = self.get_listen_friends()
        for friend in friends:
            if friend.get('name') == friend_name:
                return friend
        return None
    
    def is_auto_reply_enabled(self) -> bool:
        """检查是否启用自动回复"""
        return self.get("wechat.auto_reply", True)
    
    def set_auto_reply(self, enabled: bool):
        """设置自动回复开关"""
        self.set("wechat.auto_reply", enabled)
    
    def get_reply_delay(self) -> float:
        """获取回复延迟"""
        return self.get("wechat.reply_delay", 1.0)
    
    def set_reply_delay(self, delay: float):
        """设置回复延迟"""
        self.set("wechat.reply_delay", max(0.1, delay))
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.get("wechat.debug", False)
    
    def set_debug(self, enabled: bool):
        """设置调试模式"""
        self.set("wechat.debug", enabled)
    
    def get_current_llm(self) -> str:
        """获取当前LLM模型"""
        return self.get("general.current_llm", "deepseek")
    
    def set_current_llm(self, model_name: str):
        """设置当前LLM模型"""
        self.set("general.current_llm", model_name)
    
    def get_current_agent(self) -> str:
        """获取当前Agent"""
        return self.get("general.current_agent", "default")
    
    def set_current_agent(self, agent_name: str):
        """设置当前Agent"""
        self.set("general.current_agent", agent_name)
    
    def get_enabled_llm_models(self) -> Dict[str, Dict[str, Any]]:
        """获取已启用的LLM模型配置"""
        all_models = self.get_llm_config()
        enabled_models = {}
        
        for model_name, config in all_models.items():
            if config.get("enabled", False) and config.get("api_key"):
                enabled_models[model_name] = config
        
        return enabled_models
    
    def validate_config(self) -> Dict[str, list]:
        """验证配置的有效性"""
        errors = {
            "llm_errors": [],
            "agent_errors": [],
            "general_errors": []
        }
        
        # 验证LLM配置
        enabled_models = self.get_enabled_llm_models()
        if not enabled_models:
            errors["llm_errors"].append("没有启用的LLM模型")
        
        # 验证当前LLM是否可用
        current_llm = self.get_current_llm()
        if current_llm not in enabled_models:
            errors["llm_errors"].append(f"当前LLM模型 '{current_llm}' 未启用或配置不完整")
        
        # 验证Agent配置
        agents = self.get_agent_config()
        if not agents:
            errors["agent_errors"].append("没有配置的Agent")
        
        current_agent = self.get_current_agent()
        if current_agent not in agents:
            errors["agent_errors"].append(f"当前Agent '{current_agent}' 不存在")
        
        return errors
    
    def export_config(self, file_path: str):
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str):
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # 合并配置（保留现有配置的结构）
            self._merge_config(self.config, imported_config)
            self.save_config()
            return True
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
    
    def _merge_config(self, target: dict, source: dict):
        """递归合并配置字典"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value

    # 日志管理方法
    def load_logs(self):
        """加载日志文件"""
        try:
            if self.logs_file.exists():
                with open(self.logs_file, 'r', encoding='utf-8') as f:
                    self.logs = json.load(f)
            else:
                self.logs = []
        except Exception as e:
            print(f"加载日志文件失败: {e}")
            self.logs = []

    def save_logs(self):
        """保存日志到文件"""
        try:
            # 确保目录存在
            self.logs_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.logs_file, 'w', encoding='utf-8') as f:
                json.dump(self.logs, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存日志文件失败: {e}")

    def add_log(self, friend_name: str, content: str, log_type: str = "received",
                agent_name: str = None, response: str = None):
        """添加日志记录"""
        log_entry = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now().isoformat(),
            "friend_name": friend_name,
            "content": content,
            "type": log_type,  # "received" or "sent"
            "agent_name": agent_name,
            "response": response
        }

        self.logs.append(log_entry)

        # 限制日志数量，保留最新的1000条
        if len(self.logs) > 1000:
            self.logs = self.logs[-1000:]

        self.save_logs()
        return log_entry

    def get_logs(self, friend_filter: str = None, date_filter: str = None,
                 type_filter: str = None, page: int = 1, per_page: int = 20):
        """获取日志记录"""
        filtered_logs = self.logs.copy()

        # 应用筛选条件
        if friend_filter:
            filtered_logs = [log for log in filtered_logs if log.get("friend_name") == friend_filter]

        if date_filter:
            filtered_logs = [log for log in filtered_logs
                           if log.get("timestamp", "").startswith(date_filter)]

        if type_filter:
            filtered_logs = [log for log in filtered_logs if log.get("type") == type_filter]

        # 按时间倒序排列
        filtered_logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)

        # 分页
        total = len(filtered_logs)
        start = (page - 1) * per_page
        end = start + per_page
        page_logs = filtered_logs[start:end]

        return {
            "logs": page_logs,
            "pagination": {
                "current_page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": (total + per_page - 1) // per_page
            }
        }

    def get_log_by_id(self, log_id: str):
        """根据ID获取日志记录"""
        for log in self.logs:
            if log.get("id") == log_id:
                return log
        return None

    def clear_logs(self):
        """清空所有日志"""
        self.logs = []
        self.save_logs()


# 全局配置实例
config_manager = ConfigManager()
