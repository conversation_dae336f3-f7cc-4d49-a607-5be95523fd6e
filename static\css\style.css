/* 微信智能机器人 - 自定义样式 */

/* 全局样式 */
:root {
    --primary-color: #4e73df;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --secondary-color: #858796;
    --dark-color: #5a5c69;
    --light-color: #f8f9fc;
}

body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
}

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
    min-height: 100vh;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 1rem;
    border-radius: 0.35rem;
    margin: 0.25rem 0;
    transition: all 0.3s;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 1.2rem;
    text-align: center;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-running {
    background-color: var(--success-color);
    animation: pulse 2s infinite;
}

.status-stopped {
    background-color: var(--secondary-color);
}

.status-error {
    background-color: var(--danger-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(28, 200, 138, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(28, 200, 138, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(28, 200, 138, 0);
    }
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 600;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2653d4;
    border-color: #2653d4;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #17a673;
}

/* 表格样式 */
.table {
    color: var(--dark-color);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--dark-color);
    background-color: #f8f9fc;
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.05);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
}

.notification .alert {
    margin-bottom: 10px;
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

.modal-footer {
    border-top: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* 表单样式 */
.form-control {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select {
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 徽章样式 */
.badge {
    font-weight: 600;
    border-radius: 0.35rem;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: 0.35rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 0.35rem;
}

/* 日志容器样式 */
.log-container {
    background-color: #2d3748;
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.35rem;
    overflow-y: auto;
    max-height: 400px;
}

.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #4a5568;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
    background: #718096;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 0.35rem;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    border-radius: 0.35rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
}

.dropdown-item:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 特殊效果 */
.glow {
    box-shadow: 0 0 20px rgba(78, 115, 223, 0.5);
}

.shake {
    animation: shake 0.5s;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 0.15em solid orange;
    white-space: nowrap;
    margin: 0 auto;
    letter-spacing: 0.15em;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: orange; }
}
