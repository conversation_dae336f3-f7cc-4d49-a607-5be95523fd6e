{% extends "base.html" %}

{% block title %}微信智能机器人 - 首页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 欢迎横幅 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white text-center py-5">
                    <h1 class="display-4 mb-3">
                        <i class="fas fa-robot"></i>
                        微信智能机器人
                    </h1>
                    <p class="lead mb-4">基于AI大模型的智能微信聊天助手</p>
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <i class="fas fa-comments fa-2x mb-2"></i>
                                    <div>智能对话</div>
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <div>好友管理</div>
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-brain fa-2x mb-2"></i>
                                    <div>AI记忆</div>
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-tasks fa-2x mb-2"></i>
                                    <div>任务管理</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="fas fa-bolt text-warning"></i>
                快速操作
            </h3>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-hover h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-play-circle fa-3x text-success mb-3"></i>
                    <h5 class="card-title">启动机器人</h5>
                    <p class="card-text text-muted">开始监听微信消息并自动回复</p>
                    <button class="btn btn-success" onclick="startBot()">
                        <i class="fas fa-play"></i> 启动
                    </button>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-hover h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-cog fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">系统配置</h5>
                    <p class="card-text text-muted">配置LLM模型和API密钥</p>
                    <a href="{{ url_for('setup') }}" class="btn btn-primary">
                        <i class="fas fa-cog"></i> 配置
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-hover h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                    <h5 class="card-title">好友管理</h5>
                    <p class="card-text text-muted">添加或删除监听的好友</p>
                    <a href="{{ url_for('friends') }}" class="btn btn-info">
                        <i class="fas fa-users"></i> 管理
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card card-hover h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-tachometer-alt fa-3x text-warning mb-3"></i>
                    <h5 class="card-title">控制面板</h5>
                    <p class="card-text text-muted">查看运行状态和统计信息</p>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-warning">
                        <i class="fas fa-tachometer-alt"></i> 查看
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 功能特性 -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="mb-3">
                <i class="fas fa-star text-warning"></i>
                功能特性
            </h3>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-robot text-primary"></i>
                        多模型支持
                    </h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> DeepSeek API</li>
                        <li><i class="fas fa-check text-success"></i> ChatGPT API</li>
                        <li><i class="fas fa-check text-success"></i> Qwen API</li>
                        <li><i class="fas fa-check text-success"></i> 统一调用接口</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-user-friends text-info"></i>
                        智能交互
                    </h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> 最多监听40个好友</li>
                        <li><i class="fas fa-check text-success"></i> 个性化Agent配置</li>
                        <li><i class="fas fa-check text-success"></i> 智能记忆功能</li>
                        <li><i class="fas fa-check text-success"></i> 用户行为分析</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-tasks text-warning"></i>
                        任务管理
                    </h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> 每日任务生成</li>
                        <li><i class="fas fa-check text-success"></i> 任务完成提醒</li>
                        <li><i class="fas fa-check text-success"></i> 任务历史记录</li>
                        <li><i class="fas fa-check text-success"></i> 智能任务分析</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-shield-alt text-success"></i>
                        安全可靠
                    </h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success"></i> API密钥加密存储</li>
                        <li><i class="fas fa-check text-success"></i> 配置文件备份</li>
                        <li><i class="fas fa-check text-success"></i> 异常处理机制</li>
                        <li><i class="fas fa-check text-success"></i> 实时状态监控</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 使用指南 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-book text-primary"></i>
                        快速开始指南
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <span class="h4 mb-0">1</span>
                                </div>
                                <h6 class="mt-2">配置LLM模型</h6>
                                <p class="text-muted small">在系统配置页面添加您的API密钥</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <span class="h4 mb-0">2</span>
                                </div>
                                <h6 class="mt-2">添加监听好友</h6>
                                <p class="text-muted small">在好友管理页面添加要监听的微信好友</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <span class="h4 mb-0">3</span>
                                </div>
                                <h6 class="mt-2">启动机器人</h6>
                                <p class="text-muted small">点击启动按钮开始智能聊天服务</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function startBot() {
        fetch('/api/bot/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '{{ url_for("dashboard") }}';
                }, 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('启动失败:', error);
            showNotification('启动失败，请检查配置', 'error');
        });
    }
</script>
{% endblock %}
