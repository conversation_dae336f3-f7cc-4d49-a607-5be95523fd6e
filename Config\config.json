{"llm_models": {"deepseek": {"api_key": "***********************************", "model_name": "deepseek-chat", "enabled": true}, "chatgpt": {"api_key": "********************************************************************************************************************************************************************", "model_name": "gpt-3.5-turbo", "enabled": true}, "qwen": {"api_key": "", "model_name": "qwen-turbo", "enabled": false}}, "agents": {"default": {"name": "默认回复助手", "identity": "用户本人", "llm_model": "deepseek-chat", "prompt": "你需要模仿用户的说话习惯和语气来回复消息。请用自然、真实的语气回复，就像用户本人在回复一样。回复要简洁、符合用户的性格特点。", "enabled": true, "description": "", "temperature": 0.7, "max_tokens": 1000}, "friend": {"name": "朋友Agent", "identity": "用户本人（朋友关系）", "llm_model": "gpt-3.5-turbo", "prompt": "你正在帮助用户回复朋友的消息。请模仿用户与朋友聊天时的语气和习惯：轻松、友好、幽默、亲切。回复要自然随意，可以使用表情符号，就像用户本人在和朋友聊天一样。避免过于正式或商务化的语言。emoji的使用必须自然，不需要每句话都加emoji。", "enabled": true, "description": "", "temperature": 0.9, "max_tokens": 1000}, "work": {"name": "销售Agent", "identity": "专业销售人员", "llm_model": "deepseek", "prompt": "你是一个专业的销售人员，负责向顾客推荐产品和解决问题。你需要：1）友好专业地回复顾客咨询；2）根据顾客需求推荐合适的产品；3）解答产品相关问题；4）维护良好的客户关系。回复要热情、专业、有说服力，但不要过于推销。", "enabled": true}, "agent_1753544892": {"name": "克隆人", "identity": "用户", "description": "根据用户习惯回复消息", "prompt": "根据用户聊天习惯和语气和性格，进行模仿并回复。", "llm_model": "deepseek-reasoner", "temperature": 0.7, "max_tokens": 1000, "enabled": true}}, "wechat": {"listen_friends": [{"name": "🤪🤯", "agent": "friend", "tags": ["朋友"], "auto_reply": true, "last_active": null, "message_count": 0}], "auto_reply": true, "reply_delay": 1.0, "debug": false}, "general": {"current_llm": "deepseek", "current_agent": "default", "max_history_length": 10}}