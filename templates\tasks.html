{% extends "base.html" %}

{% block title %}任务管理 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tasks text-primary"></i>
        任务管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" onclick="generateDailyTasks()">
                <i class="fas fa-magic"></i> 生成今日任务
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                <i class="fas fa-plus"></i> 添加任务
            </button>
        </div>
    </div>
</div>

<!-- 任务统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            今日任务
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-tasks">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            已完成
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="completed-tasks">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            进行中
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="in-progress-tasks">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            完成率
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="completion-rate">0%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i>
                    任务列表
                </h6>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-secondary active" onclick="filterTasks('all')">全部</button>
                    <button class="btn btn-outline-secondary" onclick="filterTasks('today')">今日</button>
                    <button class="btn btn-outline-secondary" onclick="filterTasks('pending')">待办</button>
                    <button class="btn btn-outline-secondary" onclick="filterTasks('completed')">已完成</button>
                </div>
            </div>
            <div class="card-body">
                <div id="tasks-container">
                    <!-- 示例任务 -->
                    <div class="task-item border-bottom py-3" data-status="pending" data-date="2024-01-20">
                        <div class="d-flex align-items-start">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="task-1" onchange="toggleTask(1)">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">检查微信好友消息回复情况</h6>
                                <p class="text-muted mb-2">确保所有监听的好友消息都得到及时回复，检查AI回复质量</p>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-warning me-2">进行中</span>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-calendar"></i>
                                        今天
                                    </small>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-tag"></i>
                                        日常维护
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        预计30分钟
                                    </small>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editTask(1)">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="deleteTask(1)">
                                        <i class="fas fa-trash"></i> 删除
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="task-item border-bottom py-3" data-status="completed" data-date="2024-01-20">
                        <div class="d-flex align-items-start">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="task-2" checked onchange="toggleTask(2)">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 text-decoration-line-through text-muted">更新LLM模型配置</h6>
                                <p class="text-muted mb-2">检查并更新DeepSeek、ChatGPT等模型的API配置</p>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-success me-2">已完成</span>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-calendar"></i>
                                        今天
                                    </small>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-tag"></i>
                                        配置管理
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-check"></i>
                                        已完成
                                    </small>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editTask(2)">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="deleteTask(2)">
                                        <i class="fas fa-trash"></i> 删除
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="task-item py-3" data-status="pending" data-date="2024-01-21">
                        <div class="d-flex align-items-start">
                            <div class="form-check me-3">
                                <input class="form-check-input" type="checkbox" id="task-3" onchange="toggleTask(3)">
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">优化Agent回复策略</h6>
                                <p class="text-muted mb-2">根据用户反馈调整不同Agent的回复风格和策略</p>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-secondary me-2">待办</span>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-calendar"></i>
                                        明天
                                    </small>
                                    <small class="text-muted me-3">
                                        <i class="fas fa-tag"></i>
                                        优化改进
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        预计60分钟
                                    </small>
                                </div>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="editTask(3)">
                                        <i class="fas fa-edit"></i> 编辑
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="deleteTask(3)">
                                        <i class="fas fa-trash"></i> 删除
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 空状态 -->
                <div id="empty-state" class="text-center py-5" style="display: none;">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无任务</h5>
                    <p class="text-muted">添加您的第一个任务或生成今日任务</p>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success" onclick="generateDailyTasks()">
                            <i class="fas fa-magic"></i> 生成今日任务
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTaskModal">
                            <i class="fas fa-plus"></i> 添加任务
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i>
                    添加新任务
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-task-form">
                    <div class="mb-3">
                        <label for="task-title" class="form-label">任务标题 *</label>
                        <input type="text" class="form-control" id="task-title" 
                               placeholder="请输入任务标题" required>
                    </div>
                    <div class="mb-3">
                        <label for="task-description" class="form-label">任务描述</label>
                        <textarea class="form-control" id="task-description" rows="3" 
                                  placeholder="详细描述任务内容和要求"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task-category" class="form-label">任务分类</label>
                                <select class="form-select" id="task-category">
                                    <option value="日常维护">日常维护</option>
                                    <option value="配置管理">配置管理</option>
                                    <option value="优化改进">优化改进</option>
                                    <option value="问题修复">问题修复</option>
                                    <option value="功能开发">功能开发</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task-priority" class="form-label">优先级</label>
                                <select class="form-select" id="task-priority">
                                    <option value="低">低</option>
                                    <option value="中" selected>中</option>
                                    <option value="高">高</option>
                                    <option value="紧急">紧急</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task-due-date" class="form-label">截止日期</label>
                                <input type="date" class="form-control" id="task-due-date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="task-estimated-time" class="form-label">预计时间(分钟)</label>
                                <input type="number" class="form-control" id="task-estimated-time" 
                                       placeholder="30" min="5" max="480">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addTask()">
                    <i class="fas fa-plus"></i> 添加任务
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.text-xs { font-size: 0.7rem; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
.task-item { transition: all 0.3s; }
.task-item:hover { background-color: rgba(0,0,0,0.02); }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let tasks = [];
    let currentFilter = 'all';
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadTasks();
        updateStatistics();
    });
    
    // 加载任务列表
    function loadTasks() {
        // 这里应该从API获取任务列表
        // 暂时使用示例数据
        tasks = [
            {
                id: 1,
                title: '检查微信好友消息回复情况',
                description: '确保所有监听的好友消息都得到及时回复，检查AI回复质量',
                status: 'pending',
                category: '日常维护',
                priority: '中',
                dueDate: '2024-01-20',
                estimatedTime: 30,
                completed: false
            },
            {
                id: 2,
                title: '更新LLM模型配置',
                description: '检查并更新DeepSeek、ChatGPT等模型的API配置',
                status: 'completed',
                category: '配置管理',
                priority: '高',
                dueDate: '2024-01-20',
                estimatedTime: 45,
                completed: true
            },
            {
                id: 3,
                title: '优化Agent回复策略',
                description: '根据用户反馈调整不同Agent的回复风格和策略',
                status: 'pending',
                category: '优化改进',
                priority: '中',
                dueDate: '2024-01-21',
                estimatedTime: 60,
                completed: false
            }
        ];
        
        renderTasks();
        updateStatistics();
    }
    
    // 渲染任务列表
    function renderTasks() {
        const container = document.getElementById('tasks-container');
        const filteredTasks = filterTasksByType(tasks, currentFilter);
        
        if (filteredTasks.length === 0) {
            container.innerHTML = '';
            document.getElementById('empty-state').style.display = 'block';
            return;
        }
        
        document.getElementById('empty-state').style.display = 'none';
        
        container.innerHTML = filteredTasks.map(task => `
            <div class="task-item border-bottom py-3" data-status="${task.status}" data-date="${task.dueDate}">
                <div class="d-flex align-items-start">
                    <div class="form-check me-3">
                        <input class="form-check-input" type="checkbox" id="task-${task.id}" 
                               ${task.completed ? 'checked' : ''} onchange="toggleTask(${task.id})">
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1 ${task.completed ? 'text-decoration-line-through text-muted' : ''}">${task.title}</h6>
                        <p class="text-muted mb-2">${task.description}</p>
                        <div class="d-flex align-items-center">
                            <span class="badge ${getStatusBadgeClass(task.status)} me-2">${getStatusText(task.status)}</span>
                            <small class="text-muted me-3">
                                <i class="fas fa-calendar"></i>
                                ${formatDate(task.dueDate)}
                            </small>
                            <small class="text-muted me-3">
                                <i class="fas fa-tag"></i>
                                ${task.category}
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                ${task.completed ? '已完成' : `预计${task.estimatedTime}分钟`}
                            </small>
                        </div>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="editTask(${task.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="deleteTask(${task.id})">
                                <i class="fas fa-trash"></i> 删除
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    // 更新统计信息
    function updateStatistics() {
        const today = new Date().toISOString().split('T')[0];
        const todayTasks = tasks.filter(task => task.dueDate === today);
        const completedTasks = tasks.filter(task => task.completed);
        const inProgressTasks = tasks.filter(task => !task.completed);
        
        document.getElementById('today-tasks').textContent = todayTasks.length;
        document.getElementById('completed-tasks').textContent = completedTasks.length;
        document.getElementById('in-progress-tasks').textContent = inProgressTasks.length;
        
        const completionRate = tasks.length > 0 ? Math.round((completedTasks.length / tasks.length) * 100) : 0;
        document.getElementById('completion-rate').textContent = completionRate + '%';
    }
    
    // 过滤任务
    function filterTasks(type) {
        currentFilter = type;
        
        // 更新按钮状态
        document.querySelectorAll('.btn-group .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
        
        renderTasks();
    }
    
    // 根据类型过滤任务
    function filterTasksByType(tasks, type) {
        const today = new Date().toISOString().split('T')[0];
        
        switch (type) {
            case 'today':
                return tasks.filter(task => task.dueDate === today);
            case 'pending':
                return tasks.filter(task => !task.completed);
            case 'completed':
                return tasks.filter(task => task.completed);
            default:
                return tasks;
        }
    }
    
    // 切换任务状态
    function toggleTask(taskId) {
        const task = tasks.find(t => t.id === taskId);
        if (task) {
            task.completed = !task.completed;
            task.status = task.completed ? 'completed' : 'pending';
            renderTasks();
            updateStatistics();
            
            const action = task.completed ? '完成' : '重新开始';
            showNotification(`任务已${action}`, 'success');
        }
    }
    
    // 添加任务
    function addTask() {
        const title = document.getElementById('task-title').value.trim();
        const description = document.getElementById('task-description').value.trim();
        const category = document.getElementById('task-category').value;
        const priority = document.getElementById('task-priority').value;
        const dueDate = document.getElementById('task-due-date').value;
        const estimatedTime = parseInt(document.getElementById('task-estimated-time').value) || 30;
        
        if (!title) {
            showNotification('请输入任务标题', 'warning');
            return;
        }
        
        const newTask = {
            id: Date.now(),
            title: title,
            description: description,
            status: 'pending',
            category: category,
            priority: priority,
            dueDate: dueDate || new Date().toISOString().split('T')[0],
            estimatedTime: estimatedTime,
            completed: false
        };
        
        tasks.push(newTask);
        renderTasks();
        updateStatistics();
        
        // 清空表单
        document.getElementById('add-task-form').reset();
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addTaskModal'));
        modal.hide();
        
        showNotification('任务添加成功', 'success');
    }
    
    // 生成今日任务
    function generateDailyTasks() {
        const dailyTasks = [
            {
                title: '检查机器人运行状态',
                description: '确认微信机器人正常运行，检查连接状态',
                category: '日常维护',
                estimatedTime: 15
            },
            {
                title: '查看好友消息回复情况',
                description: '检查所有监听好友的消息回复质量和及时性',
                category: '日常维护',
                estimatedTime: 30
            },
            {
                title: '更新AI模型配置',
                description: '检查并更新LLM模型的API配置和参数',
                category: '配置管理',
                estimatedTime: 20
            }
        ];
        
        const today = new Date().toISOString().split('T')[0];
        let addedCount = 0;
        
        dailyTasks.forEach(taskTemplate => {
            // 检查是否已存在相同的今日任务
            const exists = tasks.some(task => 
                task.title === taskTemplate.title && task.dueDate === today
            );
            
            if (!exists) {
                const newTask = {
                    id: Date.now() + Math.random(),
                    title: taskTemplate.title,
                    description: taskTemplate.description,
                    status: 'pending',
                    category: taskTemplate.category,
                    priority: '中',
                    dueDate: today,
                    estimatedTime: taskTemplate.estimatedTime,
                    completed: false
                };
                
                tasks.push(newTask);
                addedCount++;
            }
        });
        
        if (addedCount > 0) {
            renderTasks();
            updateStatistics();
            showNotification(`已生成${addedCount}个今日任务`, 'success');
        } else {
            showNotification('今日任务已存在，无需重复生成', 'info');
        }
    }
    
    // 辅助函数
    function getStatusBadgeClass(status) {
        const classes = {
            'pending': 'bg-warning',
            'completed': 'bg-success',
            'in-progress': 'bg-primary'
        };
        return classes[status] || 'bg-secondary';
    }
    
    function getStatusText(status) {
        const texts = {
            'pending': '待办',
            'completed': '已完成',
            'in-progress': '进行中'
        };
        return texts[status] || '未知';
    }
    
    function formatDate(dateString) {
        const today = new Date().toISOString().split('T')[0];
        const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        
        if (dateString === today) return '今天';
        if (dateString === tomorrow) return '明天';
        
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
    }
    
    function editTask(taskId) {
        console.log('编辑任务:', taskId);
        showNotification('编辑功能开发中', 'info');
    }
    
    function deleteTask(taskId) {
        if (confirm('确定要删除这个任务吗？')) {
            tasks = tasks.filter(task => task.id !== taskId);
            renderTasks();
            updateStatistics();
            showNotification('任务已删除', 'success');
        }
    }
</script>
{% endblock %}
