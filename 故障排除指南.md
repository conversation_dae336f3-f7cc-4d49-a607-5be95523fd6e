# 微信智能机器人 - 故障排除指南

## 常见问题及解决方案

### 1. Python环境问题

#### 问题：提示"未检测到Python"
**解决方案：**
1. 下载并安装Python 3.8或更高版本：https://www.python.org/downloads/
2. 安装时务必勾选"Add Python to PATH"选项
3. 安装完成后重启命令提示符
4. 验证安装：在命令提示符中输入 `python --version`

#### 问题：Python版本过低
**解决方案：**
1. 卸载旧版本Python
2. 安装Python 3.8或更高版本
3. 确保PATH环境变量正确设置

### 2. 依赖安装问题

#### 问题：pip安装依赖失败
**解决方案：**
1. 升级pip：`python -m pip install --upgrade pip`
2. 使用国内镜像源：
   ```
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
   ```
3. 如果仍然失败，运行 `install_dependencies.bat` 进行分步安装

#### 问题：pywin32安装失败
**解决方案：**
1. 以管理员身份运行命令提示符
2. 手动安装：`pip install pywin32`
3. 如果仍然失败，从官网下载对应版本的whl文件手动安装

#### 问题：某些包版本冲突
**解决方案：**
1. 创建虚拟环境：
   ```
   python -m venv wxauto_env
   wxauto_env\Scripts\activate
   pip install -r requirements.txt
   ```
2. 在虚拟环境中运行程序

### 3. 配置问题

#### 问题：配置向导失败
**解决方案：**
1. 确保Config目录存在
2. 检查是否有写入权限
3. 手动创建config.json文件（参考Config目录下的示例）

#### 问题：LLM模型配置错误
**解决方案：**
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 验证API服务是否可用

### 4. 运行时问题

#### 问题：Web界面无法访问
**解决方案：**
1. 检查端口5000是否被占用
2. 确认防火墙设置
3. 尝试使用其他端口

#### 问题：微信连接失败
**解决方案：**
1. 确保微信PC版已启动并登录
2. 检查微信版本是否兼容
3. 重启微信和程序

### 5. 权限问题

#### 问题：需要管理员权限
**解决方案：**
1. 右键点击start.bat，选择"以管理员身份运行"
2. 或在管理员命令提示符中运行

### 6. 网络问题

#### 问题：无法连接到API服务
**解决方案：**
1. 检查网络连接
2. 确认代理设置
3. 验证API服务状态

## 环境要求

- **操作系统：** Windows 10/11
- **Python版本：** 3.8 - 3.14
- **内存：** 至少2GB可用内存
- **网络：** 稳定的互联网连接
- **微信：** 微信PC版（最新版本）

## 获取帮助

如果以上解决方案都无法解决您的问题，请：

1. 查看详细错误日志（wxauto_logs目录）
2. 记录具体的错误信息
3. 提供系统环境信息（Python版本、操作系统等）

## 联系支持

- 项目地址：查看README.md文件
- 问题反馈：通过项目仓库提交Issue
