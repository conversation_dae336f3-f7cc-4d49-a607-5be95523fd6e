{% extends "base.html" %}

{% block title %}对话日志 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-history"></i>
        对话日志
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i>
                刷新日志
            </button>
            <button type="button" class="btn btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash"></i>
                清空日志
            </button>
        </div>
    </div>
</div>

<!-- 筛选器 -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-filter"></i>
                    筛选条件
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label for="friend-filter" class="form-label">好友筛选</label>
                        <select class="form-select" id="friend-filter">
                            <option value="">全部好友</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date-filter" class="form-label">日期筛选</label>
                        <input type="date" class="form-control" id="date-filter">
                    </div>
                    <div class="col-md-3">
                        <label for="type-filter" class="form-label">消息类型</label>
                        <select class="form-select" id="type-filter">
                            <option value="">全部类型</option>
                            <option value="received">接收消息</option>
                            <option value="sent">发送消息</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn btn-primary" onclick="applyFilters()">
                            <i class="fas fa-search"></i>
                            应用筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 对话日志列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-comments"></i>
                    对话记录
                    <span class="badge bg-primary ms-2" id="log-count">0</span>
                </h6>
            </div>
            <div class="card-body">
                <div id="logs-container">
                    <!-- 日志内容将在这里动态加载 -->
                    <div class="text-center text-muted py-5" id="empty-state">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>暂无对话记录</p>
                        <small>开始监听好友消息后，对话记录将显示在这里</small>
                    </div>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="日志分页" id="pagination-container" style="display: none;">
                    <ul class="pagination justify-content-center mt-4" id="pagination">
                        <!-- 分页按钮将在这里动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 消息详情模态框 -->
<div class="modal fade" id="messageDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-comment-dots"></i>
                    消息详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>好友:</strong>
                        <span id="detail-friend"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>时间:</strong>
                        <span id="detail-time"></span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>类型:</strong>
                        <span id="detail-type"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>Agent:</strong>
                        <span id="detail-agent"></span>
                    </div>
                </div>
                <hr>
                <div class="mb-3">
                    <strong>消息内容:</strong>
                    <div class="border rounded p-3 mt-2" id="detail-content" style="background-color: #f8f9fa; white-space: pre-wrap;"></div>
                </div>
                <div id="detail-response" style="display: none;">
                    <strong>AI回复:</strong>
                    <div class="border rounded p-3 mt-2" id="detail-response-content" style="background-color: #e3f2fd; white-space: pre-wrap;"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let totalPages = 1;
    let currentFilters = {};
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadFriendsList();
        loadLogs();
        
        // 设置默认日期为今天
        document.getElementById('date-filter').value = new Date().toISOString().split('T')[0];
    });
    
    // 加载好友列表用于筛选
    function loadFriendsList() {
        fetch('/api/friends')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const select = document.getElementById('friend-filter');
                    select.innerHTML = '<option value="">全部好友</option>';
                    
                    data.data.forEach(friend => {
                        const option = document.createElement('option');
                        option.value = friend.name || friend;
                        option.textContent = friend.name || friend;
                        select.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('加载好友列表失败:', error);
            });
    }
    
    // 加载对话日志
    function loadLogs(page = 1) {
        const params = new URLSearchParams({
            page: page,
            ...currentFilters
        });
        
        fetch(`/api/logs?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderLogs(data.data.logs);
                    updatePagination(data.data.pagination);
                    document.getElementById('log-count').textContent = data.data.pagination.total;
                } else {
                    console.error('加载日志失败:', data.message);
                    showEmptyState();
                }
            })
            .catch(error => {
                console.error('加载日志失败:', error);
                showEmptyState();
            });
    }
    
    // 渲染日志列表
    function renderLogs(logs) {
        const container = document.getElementById('logs-container');
        const emptyState = document.getElementById('empty-state');

        if (!logs || logs.length === 0) {
            showEmptyState();
            return;
        }

        emptyState.style.display = 'none';

        // 按好友分组消息
        const groupedLogs = groupLogsByFriend(logs);
        const logsHtml = Object.keys(groupedLogs).map(friendName =>
            createFriendGroupHtml(friendName, groupedLogs[friendName])
        ).join('');
        container.innerHTML = logsHtml;
    }

    // 按好友分组日志
    function groupLogsByFriend(logs) {
        const grouped = {};
        logs.forEach(log => {
            if (!grouped[log.friend_name]) {
                grouped[log.friend_name] = [];
            }
            grouped[log.friend_name].push(log);
        });

        // 按时间排序每个好友的消息
        Object.keys(grouped).forEach(friendName => {
            grouped[friendName].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        });

        return grouped;
    }

    // 创建好友分组HTML
    function createFriendGroupHtml(friendName, logs) {
        const latestLog = logs[0]; // 最新的消息
        const messageCount = logs.length;
        const latestTime = new Date(latestLog.timestamp).toLocaleString('zh-CN');

        // 统计接收和发送消息数量
        const receivedCount = logs.filter(log => log.type === 'received').length;
        const sentCount = logs.filter(log => log.type === 'sent').length;

        const groupId = `friend-group-${friendName.replace(/[^a-zA-Z0-9]/g, '')}`;

        return `
            <div class="card mb-3 friend-group">
                <div class="card-header" style="cursor: pointer;" onclick="toggleFriendGroup('${groupId}')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-user me-2"></i>
                            <strong>${friendName}</strong>
                            <span class="badge bg-secondary ms-2">${messageCount} 条消息</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <small class="text-muted me-3">
                                <i class="fas fa-arrow-down text-success me-1"></i>${receivedCount}
                                <i class="fas fa-arrow-up text-primary ms-2 me-1"></i>${sentCount}
                            </small>
                            <small class="text-muted me-3">${latestTime}</small>
                            <i class="fas fa-chevron-down toggle-icon" id="${groupId}-icon"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">最新: ${latestLog.content.substring(0, 80)}${latestLog.content.length > 80 ? '...' : ''}</small>
                    </div>
                </div>
                <div class="card-body collapse" id="${groupId}">
                    ${logs.map(log => createDetailedLogItemHtml(log)).join('')}
                </div>
            </div>
        `;
    }
    
    // 创建详细日志项HTML
    function createDetailedLogItemHtml(log) {
        const typeIcon = log.type === 'received' ? 'fa-arrow-down text-success' : 'fa-arrow-up text-primary';
        const typeText = log.type === 'received' ? '接收' : '发送';
        const timeStr = new Date(log.timestamp).toLocaleString('zh-CN');

        return `
            <div class="border-start border-3 border-${log.type === 'received' ? 'success' : 'primary'} ps-3 mb-3">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="d-flex align-items-center">
                        <i class="fas ${typeIcon} me-2"></i>
                        <span class="badge bg-${log.type === 'received' ? 'success' : 'primary'} me-2">${typeText}</span>
                        ${log.agent_name ? `<span class="badge bg-info me-2">${log.agent_name}</span>` : ''}
                    </div>
                    <small class="text-muted">${timeStr}</small>
                </div>
                <div class="message-content">
                    <p class="mb-0">${log.content}</p>
                </div>
                ${log.response ? `
                    <div class="mt-2 p-2 bg-light rounded">
                        <small class="text-muted">回复内容:</small>
                        <p class="mb-0 mt-1">${log.response}</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    // 切换好友分组显示
    function toggleFriendGroup(groupId) {
        const content = document.getElementById(groupId);
        const icon = document.getElementById(groupId + '-icon');

        if (content.classList.contains('show')) {
            content.classList.remove('show');
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        } else {
            content.classList.add('show');
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        }
    }
    
    // 显示空状态
    function showEmptyState() {
        const container = document.getElementById('logs-container');
        const emptyState = document.getElementById('empty-state');
        
        container.innerHTML = '';
        emptyState.style.display = 'block';
        
        document.getElementById('pagination-container').style.display = 'none';
        document.getElementById('log-count').textContent = '0';
    }
    
    // 更新分页
    function updatePagination(pagination) {
        currentPage = pagination.current_page;
        totalPages = pagination.total_pages;
        
        const container = document.getElementById('pagination-container');
        const paginationEl = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'block';
        
        let paginationHtml = '';
        
        // 上一页
        paginationHtml += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadLogs(${currentPage - 1})">上一页</a>
            </li>
        `;
        
        // 页码
        for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="loadLogs(${i})">${i}</a>
                </li>
            `;
        }
        
        // 下一页
        paginationHtml += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadLogs(${currentPage + 1})">下一页</a>
            </li>
        `;
        
        paginationEl.innerHTML = paginationHtml;
    }
    
    // 应用筛选条件
    function applyFilters() {
        const friendFilter = document.getElementById('friend-filter').value;
        const dateFilter = document.getElementById('date-filter').value;
        const typeFilter = document.getElementById('type-filter').value;
        
        currentFilters = {};
        if (friendFilter) currentFilters.friend = friendFilter;
        if (dateFilter) currentFilters.date = dateFilter;
        if (typeFilter) currentFilters.type = typeFilter;
        
        currentPage = 1;
        loadLogs(1);
    }
    
    // 刷新日志
    function refreshLogs() {
        loadLogs(currentPage);
        showNotification('日志已刷新', 'success');
    }
    
    // 清空日志
    function clearLogs() {
        if (confirm('确定要清空所有对话日志吗？此操作不可恢复。')) {
            fetch('/api/logs', {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('日志已清空', 'success');
                    loadLogs(1);
                } else {
                    showNotification('清空失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('清空日志失败:', error);
                showNotification('清空失败: ' + error.message, 'error');
            });
        }
    }
    
    // 显示消息详情
    function showMessageDetail(logId) {
        fetch(`/api/logs/${logId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const log = data.data;
                    
                    document.getElementById('detail-friend').textContent = log.friend_name;
                    document.getElementById('detail-time').textContent = new Date(log.timestamp).toLocaleString('zh-CN');
                    document.getElementById('detail-type').textContent = log.type === 'received' ? '接收消息' : '发送消息';
                    document.getElementById('detail-agent').textContent = log.agent_name || '无';
                    document.getElementById('detail-content').textContent = log.content;
                    
                    // 如果有AI回复，显示回复内容
                    const responseDiv = document.getElementById('detail-response');
                    const responseContent = document.getElementById('detail-response-content');
                    
                    if (log.response) {
                        responseContent.textContent = log.response;
                        responseDiv.style.display = 'block';
                    } else {
                        responseDiv.style.display = 'none';
                    }
                    
                    const modal = new bootstrap.Modal(document.getElementById('messageDetailModal'));
                    modal.show();
                } else {
                    showNotification('加载消息详情失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载消息详情失败:', error);
                showNotification('加载消息详情失败: ' + error.message, 'error');
            });
    }
</script>

<style>
    .log-item {
        cursor: pointer;
        transition: all 0.2s;
    }

    .log-item:hover {
        background-color: #f8f9fa;
        border-color: #007bff !important;
    }

    .message-preview {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .friend-group .card-header {
        transition: background-color 0.2s ease;
        cursor: pointer;
    }

    .friend-group .card-header:hover {
        background-color: #e9ecef;
    }

    .toggle-icon {
        transition: transform 0.2s ease;
    }

    .message-content {
        word-wrap: break-word;
        white-space: pre-wrap;
    }

    .border-start {
        border-left-width: 4px !important;
    }

    .collapse {
        transition: height 0.3s ease;
    }

    .collapse.show {
        display: block !important;
    }
</style>
{% endblock %}
