import time
import threading
from typing import Dict, List, Optional, Callable
from wxauto import WeChat
from .Agent import Agent, AgentManager
from .LLM import LLMManager, create_llm_manager
from Config.Config import ConfigManager
import traceback


class WeChatBot:
    """微信智能机器人，基于wxauto实现自动回复功能"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化微信机器人

        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.wx = None
        self.llm_manager = None
        self.agent_manager = None
        self.is_running = False
        self.message_handlers = {}  # 自定义消息处理器
        self.message_buffer = {}  # 消息缓冲区，用于处理多段消息
        self.message_timers = {}  # 消息定时器

        # 初始化组件
        self._init_llm_manager()
        self._init_agent_manager()
    
    def _init_llm_manager(self):
        """初始化LLM管理器"""
        try:
            enabled_models = self.config_manager.get_enabled_llm_models()
            if enabled_models:
                self.llm_manager = create_llm_manager(enabled_models)
                current_llm = self.config_manager.get_current_llm()
                if current_llm in enabled_models:
                    self.llm_manager.set_current_model(current_llm)
                print(f"LLM管理器初始化成功，可用模型: {list(enabled_models.keys())}")
            else:
                print("警告: 没有启用的LLM模型，请检查配置")
        except Exception as e:
            print(f"初始化LLM管理器失败: {e}")
    
    def _init_agent_manager(self):
        """初始化Agent管理器"""
        try:
            if not self.llm_manager:
                print("警告: LLM管理器未初始化，无法创建Agent")
                return
                
            self.agent_manager = AgentManager()
            agents_config = self.config_manager.get_agent_config()
            
            for agent_name, agent_config in agents_config.items():
                if agent_config.get("enabled", True):
                    from .Agent import create_agent_from_config
                    agent = create_agent_from_config(agent_config, self.llm_manager)
                    self.agent_manager.add_agent(agent_name, agent)
            
            current_agent = self.config_manager.get_current_agent()
            if current_agent in agents_config:
                self.agent_manager.set_default_agent(current_agent)
            
            print(f"Agent管理器初始化成功，可用Agent: {self.agent_manager.list_agents()}")
        except Exception as e:
            print(f"初始化Agent管理器失败: {e}")
    
    def start(self):
        """启动微信机器人"""
        try:
            if self.is_running:
                print("机器人已在运行中")
                return False

            # 初始化微信实例
            debug_mode = self.config_manager.is_debug_enabled()
            self.wx = WeChat(debug=debug_mode)

            # 启动监听
            self.wx.StartListening()

            # 添加监听好友
            listen_friends = self.config_manager.get_listen_friends()
            print(f"准备添加 {len(listen_friends)} 个监听好友")

            for friend in listen_friends:
                friend_name = friend.get('name', friend) if isinstance(friend, dict) else friend
                self._add_friend_listener(friend_name)

            self.is_running = True
            print("微信机器人启动成功")
            return True

        except Exception as e:
            print(f"启动微信机器人失败: {e}")
            traceback.print_exc()
            return False
    
    def stop(self):
        """停止微信机器人"""
        try:
            if not self.is_running:
                print("机器人未在运行")
                return

            # 清理所有定时器
            for timer in self.message_timers.values():
                timer.cancel()
            self.message_timers.clear()
            self.message_buffer.clear()

            if self.wx:
                self.wx.StopListening(remove=True)

            self.is_running = False
            print("微信机器人已停止")

        except Exception as e:
            print(f"停止微信机器人失败: {e}")
    
    def _add_friend_listener(self, friend_name: str):
        """添加好友监听"""
        try:
            def message_callback(msg, chat):
                self._handle_message(msg, chat, friend_name)

            result = self.wx.AddListenChat(friend_name, message_callback)
            if hasattr(result, 'nickname'):
                print(f"成功添加监听好友: {friend_name} -> {result.nickname}")
                # 记录日志
                if hasattr(self.config_manager, 'add_log'):
                    self.config_manager.add_log(friend_name, f"开始监听好友: {friend_name}", "system")
            else:
                print(f"添加监听好友失败: {friend_name} - {result}")

        except Exception as e:
            print(f"添加监听好友 {friend_name} 失败: {e}")
            traceback.print_exc()
    
    def _handle_message(self, msg, chat, friend_name: str):
        """处理接收到的消息"""
        try:
            # 过滤自己发送的消息和系统消息
            if (hasattr(msg, 'sender') and msg.sender == 'self') or \
               (hasattr(msg, 'attr') and msg.attr in ['system', 'tickle']):
                return

            # 获取消息内容
            message_content = getattr(msg, 'content', str(msg))
            if not message_content or not message_content.strip():
                return

            print(f"收到来自 {friend_name} 的消息: {message_content}")

            # 记录接收到的消息
            if hasattr(self.config_manager, 'add_log'):
                self.config_manager.add_log(friend_name, message_content, "received")

            # 检查是否启用自动回复
            if not self.config_manager.is_auto_reply_enabled():
                print(f"自动回复已禁用，不回复 {friend_name}")
                return

            # 检查是否有自定义处理器
            if friend_name in self.message_handlers:
                custom_reply = self.message_handlers[friend_name](message_content, msg, chat)
                if custom_reply:
                    self._send_reply(chat, custom_reply, friend_name)
                    return

            # 实现5秒静默处理多段消息
            self._buffer_message(friend_name, message_content, chat)

        except Exception as e:
            print(f"处理消息失败: {e}")
            traceback.print_exc()

    def _buffer_message(self, friend_name: str, message_content: str, chat):
        """缓冲消息，实现5秒静默处理多段消息"""
        try:
            # 初始化好友的消息缓冲区
            if friend_name not in self.message_buffer:
                self.message_buffer[friend_name] = []

            # 添加消息到缓冲区
            self.message_buffer[friend_name].append(message_content)

            # 取消之前的定时器
            if friend_name in self.message_timers:
                self.message_timers[friend_name].cancel()

            # 设置新的定时器，5秒后处理缓冲的消息
            timer = threading.Timer(5.0, self._process_buffered_messages, args=[friend_name, chat])
            self.message_timers[friend_name] = timer
            timer.start()

            print(f"消息已缓冲，等待5秒处理多段消息: {friend_name}")

        except Exception as e:
            print(f"缓冲消息失败: {e}")
            traceback.print_exc()

    def _process_buffered_messages(self, friend_name: str, chat):
        """处理缓冲的消息"""
        try:
            if friend_name not in self.message_buffer or not self.message_buffer[friend_name]:
                return

            # 获取所有缓冲的消息
            messages = self.message_buffer[friend_name].copy()

            # 清空缓冲区
            self.message_buffer[friend_name] = []
            if friend_name in self.message_timers:
                del self.message_timers[friend_name]

            # 合并多条消息
            combined_message = "\n".join(messages)
            print(f"处理 {friend_name} 的缓冲消息 ({len(messages)} 条): {combined_message}")

            # 获取好友对应的Agent
            friend_config = self._get_friend_config(friend_name)
            agent_name = friend_config.get('agent', 'default') if friend_config else 'default'

            # 使用Agent生成回复
            if self.agent_manager:
                reply = self.agent_manager.generate_reply(combined_message, agent_name)
                if reply:
                    # 添加延迟以模拟人类回复
                    delay = self.config_manager.get_reply_delay()
                    if delay > 0:
                        time.sleep(delay)

                    self._send_reply(chat, reply, friend_name, agent_name)
                else:
                    print(f"Agent {agent_name} 未能生成回复给 {friend_name}")
            else:
                print("Agent管理器未初始化，无法生成回复")

        except Exception as e:
            print(f"处理缓冲消息失败: {e}")
            traceback.print_exc()

        except Exception as e:
            print(f"处理消息失败: {e}")
            traceback.print_exc()
    
    def _send_reply(self, chat, reply: str, friend_name: str, agent_name: str = None):
        """发送回复消息"""
        try:
            result = chat.SendMsg(reply)
            if hasattr(result, 'success') and result.success:
                print(f"成功回复 {friend_name}: {reply}")
                # 记录发送的消息
                if hasattr(self.config_manager, 'add_log'):
                    self.config_manager.add_log(friend_name, reply, "sent", agent_name)
            else:
                print(f"回复 {friend_name} 失败: {result}")
        except Exception as e:
            print(f"发送回复失败: {e}")
            traceback.print_exc()

    def _get_friend_config(self, friend_name: str):
        """获取好友配置"""
        try:
            friends = self.config_manager.get_listen_friends()
            for friend in friends:
                if isinstance(friend, dict):
                    if friend.get('name') == friend_name:
                        return friend
                elif friend == friend_name:
                    return {'name': friend_name, 'agent': 'default'}
            return None
        except Exception as e:
            print(f"获取好友配置失败: {e}")
            return None
    
    def add_friend_listener(self, friend_name: str):
        """添加新的好友监听"""
        if not self.is_running or not self.wx:
            print("机器人未运行，无法添加监听")
            return False
        
        try:
            self._add_friend_listener(friend_name)
            self.config_manager.add_listen_friend(friend_name)
            return True
        except Exception as e:
            print(f"添加好友监听失败: {e}")
            return False
    
    def remove_friend_listener(self, friend_name: str):
        """移除好友监听"""
        if not self.is_running or not self.wx:
            print("机器人未运行，无法移除监听")
            return False
        
        try:
            result = self.wx.RemoveListenChat(friend_name)
            if hasattr(result, 'success') and result.success:
                self.config_manager.remove_listen_friend(friend_name)
                print(f"成功移除监听好友: {friend_name}")
                return True
            else:
                print(f"移除监听好友失败: {friend_name}")
                return False
        except Exception as e:
            print(f"移除好友监听失败: {e}")
            return False
    
    def set_message_handler(self, friend_name: str, handler: Callable[[str, any, any], Optional[str]]):
        """为特定好友设置自定义消息处理器
        
        Args:
            friend_name: 好友名称
            handler: 处理函数，接收(message_content, msg, chat)参数，返回回复内容或None
        """
        self.message_handlers[friend_name] = handler
    
    def remove_message_handler(self, friend_name: str):
        """移除特定好友的自定义消息处理器"""
        if friend_name in self.message_handlers:
            del self.message_handlers[friend_name]
    
    def send_message(self, friend_name: str, message: str):
        """主动发送消息给指定好友"""
        if not self.is_running or not self.wx:
            print("机器人未运行，无法发送消息")
            return False
        
        try:
            result = self.wx.SendMsg(message, who=friend_name)
            if hasattr(result, 'success') and result.success:
                print(f"成功发送消息给 {friend_name}: {message}")
                return True
            else:
                print(f"发送消息给 {friend_name} 失败: {result}")
                return False
        except Exception as e:
            print(f"发送消息失败: {e}")
            return False
    
    def get_status(self) -> Dict:
        """获取机器人状态"""
        return {
            "is_running": self.is_running,
            "listen_friends": self.config_manager.get_listen_friends(),
            "auto_reply_enabled": self.config_manager.is_auto_reply_enabled(),
            "current_llm": self.config_manager.get_current_llm(),
            "current_agent": self.config_manager.get_current_agent(),
            "available_llm_models": list(self.llm_manager.list_models()) if self.llm_manager else [],
            "available_agents": list(self.agent_manager.list_agents()) if self.agent_manager else []
        }
    
    def reload_config(self):
        """重新加载配置"""
        try:
            self.config_manager.load_config()
            self._init_llm_manager()
            self._init_agent_manager()
            print("配置重新加载成功")
            return True
        except Exception as e:
            print(f"重新加载配置失败: {e}")
            return False
    
    def keep_running(self):
        """保持运行状态"""
        if self.wx:
            self.wx.KeepRunning()
        else:
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("接收到中断信号，正在停止...")
                self.stop()
