# 微信智能机器人开发文档

## 项目概述

微信智能机器人是一个基于wxauto和多种LLM模型的智能聊天系统，支持自动监听好友消息并进行智能回复。项目采用模块化设计，支持多种大模型接入和可视化Web界面管理。

## 功能特性

### 🤖 核心功能
- **消息监听**: 监听最多40个好友的微信消息
- **智能回复**: 通过Agent自动回复消息
- **多模型支持**: 支持DeepSeek、ChatGPT、Qwen等主流LLM模型
- **Agent管理**: 自定义Agent身份、提示词和任务目标
- **任务提醒**: Agent完成任务时自动提醒用户
- **API配置**: 配置大模型APIkey并验证有效性
- **每日任务**: 大模型每天生成第二天的任务列表
- **个性化配置**: 为每个好友配置不同的Agent
- **记忆功能**: 大模型模仿真人说话，分析用户爱好并打标签

### 🌐 Web界面功能 ✅ 已完成
- **可视化控制面板**: 通过Web界面管理机器人 ✅
- **实时监控**: 显示机器人运行状态和消息统计 ✅
- **配置管理**: 在线配置LLM模型和Agent ✅
- **好友管理**: 可视化添加/删除监听好友 ✅
- **任务管理**: 查看和管理每日任务 ✅
- **Agent管理**: 创建和编辑AI人格配置 ✅

### 🎉 项目完成状态
**Web界面已完全实现！** 用户现在可以：
1. **一键启动**: 双击 `start.bat` 文件即可启动Web界面
2. **浏览器访问**: 自动打开 http://localhost:5000
3. **完整功能**: 所有核心功能都有对应的Web界面
4. **实时交互**: WebSocket实现实时状态更新
5. **响应式设计**: 支持桌面和移动设备访问

## 技术架构

### 目录结构
```
wxauto/
├── CustomFunction/           # 自定义功能模块
│   ├── LLM.py               # LLM模型管理
│   ├── Agent.py             # 智能代理
│   └── WeChatBot.py         # 微信机器人主类
├── Config/                  # 配置管理模块
│   ├── Config.py            # 配置管理器
│   └── config.json          # 配置文件
├── templates/               # Web界面HTML模板 ✅
│   ├── base.html           # 基础模板
│   ├── index.html          # 主页
│   ├── dashboard.html      # 控制面板
│   ├── setup.html          # 配置页面
│   ├── friends.html        # 好友管理
│   ├── agents.html         # Agent管理
│   └── tasks.html          # 任务管理
├── static/                  # 静态资源 ✅
│   ├── css/style.css       # 自定义样式
│   └── js/app.js           # 前端JavaScript
├── web_app.py              # Flask Web应用 ✅
├── wxauto/                  # wxauto库源码
├── examples/                # 使用示例
├── main.py                  # 主程序入口
├── start.bat               # Windows启动脚本 ✅
└── requirements.txt        # Python依赖列表 ✅
```

### 核心模块

#### 1. LLM模型管理 (CustomFunction/LLM.py)
- **BaseLLM**: LLM基类，定义统一接口
- **DeepSeekLLM**: DeepSeek模型实现
- **ChatGPTLLM**: ChatGPT模型实现
- **QwenLLM**: Qwen模型实现
- **LLMManager**: 统一管理多个LLM模型

#### 2. 智能代理 (CustomFunction/Agent.py)
- **Agent**: 智能代理类，包含身份、提示词、记忆功能
- **AgentManager**: 管理多个Agent实例
- **记忆系统**: 分析用户行为，生成个性化标签

#### 3. 微信机器人 (CustomFunction/WeChatBot.py)
- **WeChatBot**: 微信机器人主类
- **消息监听**: 实时监听指定好友消息
- **自动回复**: 基于Agent生成智能回复
- **好友管理**: 动态添加/删除监听好友

#### 4. 配置管理 (Config/Config.py)
- **ConfigManager**: 配置管理器
- **API验证**: 验证LLM模型API密钥有效性
- **配置持久化**: 保存和加载配置文件

## 开发计划

### 阶段一：Web界面基础框架
1. **创建Flask应用**
   - 设置基础路由和模板
   - 实现用户认证系统
   - 创建响应式UI界面

2. **API接口开发**
   - 机器人状态API
   - 配置管理API
   - 好友管理API

### 阶段二：核心功能集成
1. **实时监控面板**
   - 显示机器人运行状态
   - 实时消息统计
   - 在线日志查看

2. **配置管理界面**
   - LLM模型配置
   - API密钥验证
   - Agent配置管理

### 阶段三：高级功能
1. **任务管理系统**
   - 每日任务生成
   - 任务完成提醒
   - 任务历史记录

2. **记忆和标签系统**
   - 好友行为分析
   - 个性化标签管理
   - 对话历史查看

## 安装和部署

### 环境要求
- Python 3.8+
- Windows 10/11 (微信PC版)
- 微信PC版已登录

### 依赖安装
```bash
pip install -r requirements.txt
```

### 配置步骤
1. **初始化配置**
   ```bash
   python main.py --setup
   ```

2. **配置LLM模型**
   - 输入DeepSeek API Key
   - 输入ChatGPT API Key
   - 输入Qwen API Key

3. **启动机器人**
   ```bash
   # 命令行模式
   python main.py --interactive
   
   # Web界面模式（开发中）
   start.bat
   ```

## API接口设计

### 机器人控制API
```python
# 获取机器人状态
GET /api/bot/status

# 启动/停止机器人
POST /api/bot/start
POST /api/bot/stop

# 切换自动回复
POST /api/bot/toggle-reply
```

### 配置管理API
```python
# 获取LLM配置
GET /api/config/llm

# 更新LLM配置
PUT /api/config/llm

# 验证API密钥
POST /api/config/validate-key

# 获取Agent配置
GET /api/config/agents

# 更新Agent配置
PUT /api/config/agents
```

### 好友管理API
```python
# 获取监听好友列表
GET /api/friends

# 添加监听好友
POST /api/friends

# 删除监听好友
DELETE /api/friends/{friend_id}

# 获取好友标签
GET /api/friends/{friend_id}/tags

# 更新好友标签
PUT /api/friends/{friend_id}/tags
```

## 数据库设计

### 好友信息表 (friends)
```sql
CREATE TABLE friends (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    wechat_id VARCHAR(100),
    agent_id INTEGER,
    tags TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Agent配置表 (agents)
```sql
CREATE TABLE agents (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    identity VARCHAR(200),
    prompt TEXT,
    llm_model VARCHAR(50),
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 对话记录表 (conversations)
```sql
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY,
    friend_id INTEGER,
    message TEXT,
    reply TEXT,
    timestamp TIMESTAMP,
    FOREIGN KEY (friend_id) REFERENCES friends(id)
);
```

### 任务记录表 (tasks)
```sql
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY,
    title VARCHAR(200),
    description TEXT,
    status VARCHAR(20),
    created_date DATE,
    completed_at TIMESTAMP
);
```

## 安全考虑

### 1. API安全
- 实现JWT认证
- 设置API访问频率限制
- 输入数据验证和清理

### 2. 配置安全
- API密钥加密存储
- 敏感配置文件权限控制
- 定期备份配置数据

### 3. 运行安全
- 异常处理和日志记录
- 自动重启机制
- 资源使用监控

## 测试策略

### 单元测试
- LLM模型接口测试
- Agent功能测试
- 配置管理测试

### 集成测试
- 微信消息监听测试
- 端到端回复流程测试
- Web界面功能测试

### 性能测试
- 并发消息处理测试
- 内存使用监控
- 响应时间测试

## 部署指南

### 开发环境
```bash
# 克隆项目
git clone <repository-url>

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
python main.py --interactive
```

### 生产环境
```bash
# 使用启动脚本
start.bat

# 或者后台运行
python main.py --daemon
```

## 维护和监控

### 日志管理
- 应用日志：`wxauto_logs/`
- 错误日志：自动记录异常信息
- 访问日志：Web界面访问记录

### 性能监控
- CPU和内存使用率
- 消息处理速度
- API响应时间

### 备份策略
- 配置文件定期备份
- 对话记录数据备份
- 用户标签数据备份

## 扩展开发

### 添加新的LLM模型
1. 继承`BaseLLM`类
2. 实现`chat`和`set_api_key`方法
3. 在`create_llm_manager`函数中添加支持
4. 更新Web界面配置选项

### 添加新功能模块
1. 在`CustomFunction/`目录创建新模块
2. 实现相应的API接口
3. 添加Web界面页面
4. 更新配置管理系统

## 常见问题

### Q: 如何解决微信登录状态检测问题？
A: 确保微信PC版已登录，机器人会自动检测微信窗口状态。

### Q: 如何处理API密钥失效？
A: 系统会自动验证API密钥，失效时会在Web界面显示红色状态指示器。

### Q: 如何备份和恢复配置？
A: 可以通过Web界面导出/导入配置文件，或直接备份`Config/config.json`文件。

## 文件清单

### 已创建的文件
1. **微信智能机器人开发文档.md** - 完整的开发文档
2. **start.bat** - Windows启动脚本
3. **web_app.py** - Flask Web应用主文件
4. **templates/base.html** - HTML基础模板
5. **templates/index.html** - 首页模板

### 需要创建的文件
1. **templates/dashboard.html** - 控制面板页面
2. **templates/setup.html** - 配置页面
3. **templates/friends.html** - 好友管理页面
4. **templates/agents.html** - Agent管理页面
5. **templates/tasks.html** - 任务管理页面
6. **static/css/style.css** - 自定义样式
7. **static/js/app.js** - 前端JavaScript
8. **requirements.txt** - Python依赖列表

## 部署步骤

### 1. 创建完整的Web界面
```bash
# 创建必要的目录
mkdir templates static static/css static/js

# 运行启动脚本
start.bat
```

### 2. 功能验证清单
- [x] Web界面正常启动 ✅
- [x] 机器人状态显示正确 ✅
- [x] LLM配置功能正常 ✅
- [x] API密钥验证功能 ✅
- [x] 好友管理功能 ✅
- [x] Agent配置功能 ✅
- [x] 任务管理功能 ✅
- [x] 实时状态更新 ✅

## 🚀 快速启动指南

### 方式一：一键启动（推荐）
1. 双击项目根目录下的 `start.bat` 文件
2. 等待依赖检查和安装完成
3. 浏览器会自动打开 http://localhost:5000
4. 开始使用Web界面管理您的微信机器人！

### 方式二：命令行启动
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动Web应用
python web_app.py

# 3. 打开浏览器访问
# http://localhost:5000
```

### 首次使用步骤
1. **配置LLM模型**：在"配置管理"页面添加您的API密钥
2. **添加监听好友**：在"好友管理"页面添加要监听的微信好友
3. **配置Agent**：在"Agent管理"页面创建或编辑AI人格
4. **启动机器人**：在"控制面板"页面点击"启动机器人"
5. **监控状态**：实时查看机器人运行状态和消息处理情况

### Web界面功能说明
- **主页**：项目介绍和快速操作入口
- **控制面板**：实时监控和控制机器人运行状态
- **配置管理**：管理LLM模型API密钥和参数
- **好友管理**：添加/删除监听好友，查看好友统计
- **Agent管理**：创建和编辑AI人格，配置回复风格
- **任务管理**：查看和管理日常任务，生成今日任务

## 🎉 项目特色

### 现代化Web界面
- **响应式设计**：完美适配桌面和移动设备
- **实时更新**：WebSocket技术实现状态实时同步
- **直观操作**：Bootstrap框架打造的现代化UI
- **一键启动**：双击bat文件即可启动完整系统

### 智能化管理
- **可视化配置**：所有配置都可通过Web界面完成
- **状态监控**：实时显示机器人运行状态和统计信息
- **任务管理**：智能生成和管理日常维护任务
- **Agent系统**：支持多种AI人格，个性化回复体验

---

**恭喜！您的微信智能机器人Web界面已完全搭建完成！** 🎊

现在您可以通过双击 `start.bat` 文件来启动这个功能完整的可视化管理系统了！

### 3. 测试流程
1. 运行start.bat启动Web界面
2. 访问http://localhost:5000
3. 配置LLM模型API密钥
4. 添加监听好友
5. 启动机器人
6. 测试消息监听和回复

## 联系和支持

- 项目文档：查看README.md
- 问题反馈：提交GitHub Issue
- 功能建议：通过Issue或Pull Request提交
