{% extends "base.html" %}

{% block title %}控制面板 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt text-primary"></i>
        控制面板
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" id="start-btn" onclick="startBot()">
                <i class="fas fa-play"></i> 启动
            </button>
            <button type="button" class="btn btn-danger" id="stop-btn" onclick="stopBot()">
                <i class="fas fa-stop"></i> 停止
            </button>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="refreshStatus()">
            <i class="fas fa-sync-alt"></i> 刷新
        </button>
    </div>
</div>

<!-- 状态卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            机器人状态
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="bot-status-text">
                            <span class="status-indicator status-stopped"></span>
                            已停止
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-robot fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            监听好友
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="friends-count">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            当前模型
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="current-llm">未配置</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-brain fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            自动回复
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="auto-reply-status">关闭</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细信息 -->
<div class="row">
    <!-- 系统信息 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i>
                    系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-borderless">
                        <tbody>
                            <tr>
                                <td><strong>运行状态:</strong></td>
                                <td id="detailed-status">
                                    <span class="badge bg-secondary">未知</span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>当前LLM:</strong></td>
                                <td id="detailed-llm">未配置</td>
                            </tr>
                            <tr>
                                <td><strong>当前Agent:</strong></td>
                                <td id="detailed-agent">未配置</td>
                            </tr>
                            <tr>
                                <td><strong>可用模型:</strong></td>
                                <td id="enabled-models">无</td>
                            </tr>
                            <tr>
                                <td><strong>自动回复:</strong></td>
                                <td>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto-reply-toggle" onchange="toggleAutoReply()">
                                        <label class="form-check-label" for="auto-reply-toggle">
                                            启用自动回复
                                        </label>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 监听好友列表 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users"></i>
                    监听好友列表
                </h6>
                <a href="{{ url_for('friends') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-edit"></i> 管理
                </a>
            </div>
            <div class="card-body">
                <div id="friends-list">
                    <div class="text-center text-muted">
                        <i class="fas fa-user-plus fa-2x mb-2"></i>
                        <p>暂无监听好友</p>
                        <a href="{{ url_for('friends') }}" class="btn btn-sm btn-outline-primary">
                            添加好友
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-terminal"></i>
                    实时日志
                </h6>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> 清空
                </button>
            </div>
            <div class="card-body">
                <div id="log-container" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                    <div class="text-muted">等待日志输出...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
    let logCount = 0;
    
    // 启动机器人
    function startBot() {
        fetch('/api/bot/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                addLog('INFO', data.message);
                updateStatus();
            } else {
                showNotification(data.message, 'error');
                addLog('ERROR', data.message);
            }
        })
        .catch(error => {
            console.error('启动失败:', error);
            showNotification('启动失败，请检查配置', 'error');
            addLog('ERROR', '启动失败: ' + error.message);
        });
    }
    
    // 停止机器人
    function stopBot() {
        fetch('/api/bot/stop', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                addLog('INFO', data.message);
                updateStatus();
            } else {
                showNotification(data.message, 'error');
                addLog('ERROR', data.message);
            }
        })
        .catch(error => {
            console.error('停止失败:', error);
            showNotification('停止失败', 'error');
            addLog('ERROR', '停止失败: ' + error.message);
        });
    }
    
    // 切换自动回复
    function toggleAutoReply() {
        const enabled = document.getElementById('auto-reply-toggle').checked;
        // 这里应该调用API更新自动回复设置
        addLog('INFO', `自动回复已${enabled ? '开启' : '关闭'}`);
        showNotification(`自动回复已${enabled ? '开启' : '关闭'}`, 'info');
    }
    
    // 刷新状态
    function refreshStatus() {
        updateStatus();
        addLog('INFO', '状态已刷新');
    }
    
    // 更新状态显示
    function updateStatus() {
        fetch('/api/bot/status')
            .then(response => response.json())
            .then(data => {
                // 更新状态卡片
                const statusText = document.getElementById('bot-status-text');
                const indicator = statusText.querySelector('.status-indicator');

                // 使用服务器返回的状态文本和样式
                const statusTextContent = data.status_text || '未知状态';
                const statusClass = data.status_class || 'secondary';

                // 更新状态指示器
                indicator.className = `status-indicator status-${data.is_running ? 'running' : 'stopped'}`;

                // 更新状态文本
                statusText.innerHTML = `<span class="status-indicator status-${data.is_running ? 'running' : 'stopped'}"></span> ${statusTextContent}`;

                // 更新详细状态
                const badgeClass = `bg-${statusClass}`;
                document.getElementById('detailed-status').innerHTML = `<span class="badge ${badgeClass}">${statusTextContent}</span>`;

                // 更新按钮状态
                document.getElementById('start-btn').disabled = data.is_running;
                document.getElementById('stop-btn').disabled = !data.is_running;
                
                // 更新其他信息
                document.getElementById('friends-count').textContent = data.listen_friends.length;
                document.getElementById('current-llm').textContent = data.current_llm || '未配置';
                document.getElementById('detailed-llm').textContent = data.current_llm || '未配置';
                document.getElementById('detailed-agent').textContent = data.current_agent || '未配置';
                document.getElementById('enabled-models').textContent = data.enabled_models ? data.enabled_models.join(', ') : '无';
                document.getElementById('auto-reply-status').textContent = data.auto_reply_enabled ? '开启' : '关闭';
                document.getElementById('auto-reply-toggle').checked = data.auto_reply_enabled;
                
                // 更新好友列表
                updateFriendsList(data.listen_friends);
            })
            .catch(error => {
                console.error('获取状态失败:', error);
                addLog('ERROR', '获取状态失败: ' + error.message);
            });
    }
    
    // 更新好友列表显示
    function updateFriendsList(friends) {
        const container = document.getElementById('friends-list');
        
        if (friends.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-user-plus fa-2x mb-2"></i>
                    <p>暂无监听好友</p>
                    <a href="{{ url_for('friends') }}" class="btn btn-sm btn-outline-primary">
                        添加好友
                    </a>
                </div>
            `;
        } else {
            const friendsHtml = friends.map(friend => {
                // 处理好友名称，支持中文、英文、emoji等
                const friendName = typeof friend === 'object' ? (friend.name || friend) : friend;
                const agentName = typeof friend === 'object' ? friend.agent : '';

                return `
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <i class="fas fa-user text-muted me-2"></i>
                            <span style="font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;">${friendName}</span>
                            ${agentName ? `<small class="text-muted ms-2">(${agentName})</small>` : ''}
                        </div>
                        <span class="badge bg-success">监听中</span>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = friendsHtml;
        }
    }
    
    // 添加日志
    function addLog(level, message) {
        const container = document.getElementById('log-container');
        const timestamp = new Date().toLocaleTimeString();
        const levelClass = {
            'INFO': 'text-info',
            'ERROR': 'text-danger',
            'WARNING': 'text-warning',
            'SUCCESS': 'text-success'
        }[level] || 'text-muted';
        
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `
            <span class="text-muted">[${timestamp}]</span>
            <span class="${levelClass}">[${level}]</span>
            ${message}
        `;
        
        container.appendChild(logEntry);
        container.scrollTop = container.scrollHeight;
        
        // 限制日志条数
        logCount++;
        if (logCount > 100) {
            container.removeChild(container.firstChild);
            logCount--;
        }
    }
    
    // 清空日志
    function clearLogs() {
        document.getElementById('log-container').innerHTML = '<div class="text-muted">日志已清空</div>';
        logCount = 0;
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        updateStatus();
        addLog('INFO', '控制面板已加载');
        
        // 定期更新状态
        setInterval(updateStatus, 30000); // 每30秒更新一次
    });
    
    // WebSocket事件监听
    socket.on('bot_status_changed', function(data) {
        updateStatus();
        addLog('INFO', `机器人状态变更: ${data.status}`);
    });
</script>
{% endblock %}
