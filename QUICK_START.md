# 快速启动指南

## 🚀 立即开始使用微信智能聊天机器人

### 1. 检查环境

确保你已经安装了必要的依赖：

```bash
pip install openai requests wxauto
```

### 2. 验证安装

运行测试确保所有模块正常工作：

```bash
python test_bot.py
```

应该看到所有测试通过的消息。

### 3. 配置机器人

#### 方法一：使用配置向导（推荐）

```bash
python main.py --setup
```

按照提示输入你的API密钥：
- DeepSeek API Key
- ChatGPT API Key  
- Qwen API Key

#### 方法二：手动编辑配置文件

编辑 `Config/config.json` 文件，添加你的API密钥：

```json
{
  "llm_models": {
    "deepseek": {
      "api_key": "你的DeepSeek API Key",
      "model_name": "deepseek-chat",
      "enabled": true
    }
  }
}
```

### 4. 启动机器人

#### 交互模式（推荐新手）

```bash
python main.py --interactive
```

在交互模式下，你可以使用以下命令：

- `status` - 查看机器人状态
- `add 好友名` - 添加监听好友
- `remove 好友名` - 移除监听好友
- `send 好友名 消息内容` - 发送消息
- `toggle` - 切换自动回复开关
- `help` - 显示帮助
- `quit` - 退出程序

#### 后台运行模式

```bash
python main.py --daemon
```

#### 默认模式

```bash
python main.py
```

### 5. 添加监听好友

启动机器人后，使用以下方式添加要监听的好友：

#### 在交互模式下：
```
> add 张三
> add 李四
```

#### 或者直接编辑配置文件：
```json
{
  "wechat": {
    "listen_friends": ["张三", "李四"],
    "auto_reply": true
  }
}
```

### 6. 测试功能

1. 让朋友给你发送消息
2. 机器人应该会自动回复
3. 在控制台查看日志信息

### 🔧 常见问题解决

#### 问题1：无法连接微信
**解决方案：**
- 确保微信已经登录
- 确保微信窗口可见（不要最小化）
- 重启微信后再试

#### 问题2：API调用失败
**解决方案：**
- 检查API密钥是否正确
- 确认网络连接正常
- 检查是否超出API调用限制

#### 问题3：找不到好友
**解决方案：**
- 确认好友名称拼写正确
- 使用微信显示的完整昵称
- 检查好友是否在线

#### 问题4：消息不回复
**解决方案：**
- 检查自动回复是否开启：`toggle`
- 查看控制台是否有错误信息
- 确认Agent配置正确

### 📋 配置说明

#### 基本配置结构：

```json
{
  "llm_models": {
    "deepseek": {
      "api_key": "你的API密钥",
      "model_name": "deepseek-chat", 
      "enabled": true
    }
  },
  "agents": {
    "default": {
      "name": "智能助手",
      "identity": "友善的AI助手",
      "llm_model": "deepseek",
      "prompt": "你是一个友善的AI助手...",
      "enabled": true
    }
  },
  "wechat": {
    "listen_friends": ["好友1", "好友2"],
    "auto_reply": true,
    "reply_delay": 1.0,
    "debug": false
  },
  "general": {
    "current_llm": "deepseek",
    "current_agent": "default"
  }
}
```

### 🎯 使用技巧

1. **设置回复延迟**：在配置中设置 `reply_delay` 来模拟人类回复时间
2. **自定义提示词**：修改Agent的 `prompt` 来改变回复风格
3. **多Agent切换**：配置多个Agent用于不同场景
4. **调试模式**：设置 `debug: true` 获取详细日志

### 🔒 安全提醒

1. **保护API密钥**：不要将包含API密钥的配置文件提交到版本控制
2. **合理使用**：遵守各LLM服务的使用条款
3. **隐私保护**：注意保护聊天内容的隐私

### 📞 获取帮助

如果遇到问题：

1. 查看控制台错误信息
2. 运行 `python test_bot.py` 检查环境
3. 检查配置文件格式是否正确
4. 参考 `README_Bot.md` 获取详细文档

---

🎉 **恭喜！你的微信智能聊天机器人已经准备就绪！**

现在你可以享受AI助手带来的便利了！
