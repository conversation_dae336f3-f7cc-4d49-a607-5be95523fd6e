{% extends "base.html" %}

{% block title %}好友管理 - 微信智能机器人{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users text-primary"></i>
        好友管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFriendModal">
            <i class="fas fa-user-plus"></i> 添加好友
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            监听好友总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-friends">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃好友
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-friends">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            今日消息
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-messages">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            最大限制
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">40</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 好友列表 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-list"></i>
                    监听好友列表
                </h6>
                <div class="input-group" style="width: 300px;">
                    <input type="text" class="form-control" id="search-input" placeholder="搜索好友...">
                    <button class="btn btn-outline-secondary" type="button" onclick="searchFriends()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>好友名称</th>
                                <th>Agent配置</th>
                                <th>标签</th>
                                <th>最后活跃</th>
                                <th>消息数量</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="friends-table-body">
                            <!-- 动态加载好友列表 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 空状态 -->
                <div id="empty-state" class="text-center py-5" style="display: none;">
                    <i class="fas fa-user-friends fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无监听好友</h5>
                    <p class="text-muted">点击上方"添加好友"按钮开始添加要监听的微信好友</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFriendModal">
                        <i class="fas fa-user-plus"></i> 添加第一个好友
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加好友模态框 -->
<div class="modal fade" id="addFriendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    添加监听好友
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-friend-form">
                    <div class="mb-3">
                        <label for="friend-name" class="form-label">好友名称 *</label>
                        <input type="text" class="form-control" id="friend-name" 
                               placeholder="请输入微信好友的显示名称" required>
                        <div class="form-text">请输入在微信中显示的好友名称，区分大小写</div>
                    </div>
                    <div class="mb-3">
                        <label for="friend-agent" class="form-label">指定Agent</label>
                        <select class="form-select" id="friend-agent">
                            <option value="">使用默认Agent</option>
                            <!-- 动态加载Agent列表 -->
                        </select>
                        <div class="form-text">为此好友指定专用的Agent，留空则使用默认Agent</div>
                    </div>
                    <div class="mb-3">
                        <label for="friend-tags" class="form-label">初始标签</label>
                        <input type="text" class="form-control" id="friend-tags" 
                               placeholder="例如：朋友,同事,重要">
                        <div class="form-text">用逗号分隔多个标签，AI会根据对话内容自动更新标签</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="friend-auto-reply" checked>
                            <label class="form-check-label" for="friend-auto-reply">
                                启用自动回复
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addFriend()">
                    <i class="fas fa-plus"></i> 添加好友
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑好友模态框 -->
<div class="modal fade" id="editFriendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit"></i>
                    编辑好友信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="edit-friend-form">
                    <input type="hidden" id="edit-friend-id">
                    <div class="mb-3">
                        <label for="edit-friend-name" class="form-label">好友名称</label>
                        <input type="text" class="form-control" id="edit-friend-name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit-friend-agent" class="form-label">指定Agent</label>
                        <select class="form-select" id="edit-friend-agent">
                            <option value="">使用默认Agent</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit-friend-tags" class="form-label">标签</label>
                        <input type="text" class="form-control" id="edit-friend-tags">
                        <div class="form-text">用逗号分隔多个标签</div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit-friend-auto-reply">
                            <label class="form-check-label" for="edit-friend-auto-reply">
                                启用自动回复
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateFriend()">
                    <i class="fas fa-save"></i> 保存更改
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-xs { font-size: 0.7rem; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
</style>
{% endblock %}

{% block extra_js %}
<script>
    let friendsList = [];
    let agentsList = {};

    // 获取Agent显示名称
    function getAgentDisplayName(agentId) {
        if (!agentId || agentId === 'default') {
            return '默认Agent';
        }
        const agent = agentsList[agentId];
        return agent ? agent.name : agentId;
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadFriends();
        loadAgents();
    });
    
    // 加载好友列表
    function loadFriends() {
        fetch('/api/friends')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    friendsList = data.data.map(friend => {
                        // 处理新格式和旧格式的数据
                        if (typeof friend === 'string') {
                            return {
                                name: friend,
                                agent: '默认Agent',
                                tags: ['好友'],
                                lastActive: '未知',
                                messageCount: 0,
                                autoReply: true
                            };
                        } else {
                            return {
                                name: friend.name,
                                agent: friend.agent || 'default',  // 保存实际的Agent ID
                                tags: friend.tags || ['好友'],
                                lastActive: friend.last_active || '未知',
                                messageCount: friend.message_count || 0,
                                autoReply: friend.auto_reply !== false
                            };
                        }
                    });
                    renderFriendsTable();
                    updateStatistics();
                    console.log('好友列表加载成功，共', friendsList.length, '个好友');
                } else {
                    console.error('加载好友列表失败:', data.message);
                }
            })
            .catch(error => {
                console.error('加载好友列表失败:', error);
                showNotification('加载好友列表失败', 'error');
            });
    }
    
    // 加载Agent列表
    function loadAgents() {
        fetch('/api/agents')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存Agent数据到全局变量
                    agentsList = data.data;

                    const agentSelects = ['friend-agent', 'edit-friend-agent'];
                    agentSelects.forEach(selectId => {
                        const select = document.getElementById(selectId);
                        if (!select) return;

                        // 清空现有选项（保留默认选项）
                        while (select.children.length > 1) {
                            select.removeChild(select.lastChild);
                        }

                        // 添加Agent选项
                        Object.keys(data.data).forEach(agentId => {
                            const agent = data.data[agentId];
                            if (agent && agent.name) {
                                const option = document.createElement('option');
                                option.value = agentId;
                                option.textContent = agent.name;
                                select.appendChild(option);
                            }
                        });
                    });
                    console.log('Agent列表加载成功，共', Object.keys(data.data).length, '个Agent');

                    // 重新渲染好友表格以更新Agent显示名称
                    renderFriendsTable();
                } else {
                    console.error('加载Agent列表失败:', data.message);
                }
            })
            .catch(error => {
                console.error('加载Agent列表失败:', error);
            });
    }
    
    // 渲染好友表格
    function renderFriendsTable() {
        const tbody = document.getElementById('friends-table-body');
        const emptyState = document.getElementById('empty-state');
        
        if (friendsList.length === 0) {
            tbody.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }
        
        emptyState.style.display = 'none';
        
        tbody.innerHTML = friendsList.map(friend => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                            ${friend.name.charAt(0)}
                        </div>
                        <strong>${friend.name}</strong>
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">${getAgentDisplayName(friend.agent)}</span>
                </td>
                <td>
                    ${friend.tags.map(tag => `<span class="badge bg-secondary me-1">${tag}</span>`).join('')}
                </td>
                <td>
                    <small class="text-muted">${friend.lastActive}</small>
                </td>
                <td>
                    <span class="badge bg-primary">${friend.messageCount}</span>
                </td>
                <td>
                    <span class="badge ${friend.autoReply ? 'bg-success' : 'bg-secondary'}">
                        ${friend.autoReply ? '监听中' : '已暂停'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editFriend('${friend.name}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="removeFriend('${friend.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // 更新统计信息
    function updateStatistics() {
        document.getElementById('total-friends').textContent = friendsList.length;
        document.getElementById('active-friends').textContent = friendsList.filter(f => f.autoReply).length;
        document.getElementById('today-messages').textContent = friendsList.reduce((sum, f) => sum + f.messageCount, 0);
    }
    
    // 添加好友
    function addFriend() {
        const name = document.getElementById('friend-name').value.trim();
        const agent = document.getElementById('friend-agent').value;
        const tags = document.getElementById('friend-tags').value;
        const autoReply = document.getElementById('friend-auto-reply').checked;
        
        if (!name) {
            showNotification('请输入好友名称', 'warning');
            return;
        }
        
        if (friendsList.some(f => f.name === name)) {
            showNotification('该好友已在监听列表中', 'warning');
            return;
        }
        
        if (friendsList.length >= 40) {
            showNotification('监听好友数量已达上限(40个)', 'error');
            return;
        }
        
        fetch('/api/friends', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                agent: agent,
                tags: tags ? tags.split(',').map(t => t.trim()) : ['好友'],
                auto_reply: autoReply
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 添加到本地列表
                friendsList.push({
                    name: name,
                    agent: agent || 'default',  // 保存实际的Agent ID
                    tags: tags ? tags.split(',').map(t => t.trim()) : ['好友'],
                    lastActive: '刚刚添加',
                    messageCount: 0,
                    autoReply: autoReply
                });
                
                renderFriendsTable();
                updateStatistics();
                
                // 清空表单
                document.getElementById('add-friend-form').reset();
                
                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addFriendModal'));
                modal.hide();
                
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('添加好友失败:', error);
            showNotification('添加好友失败', 'error');
        });
    }
    
    // 编辑好友
    function editFriend(friendName) {
        const friend = friendsList.find(f => f.name === friendName);
        if (!friend) return;
        
        document.getElementById('edit-friend-id').value = friendName;
        document.getElementById('edit-friend-name').value = friend.name;
        // 设置Agent选择值，如果是默认Agent或未设置则为空
        const agentValue = (friend.agent === '默认Agent' || friend.agent === 'default' || !friend.agent) ? '' : friend.agent;
        document.getElementById('edit-friend-agent').value = agentValue;
        document.getElementById('edit-friend-tags').value = friend.tags.join(', ');
        document.getElementById('edit-friend-auto-reply').checked = friend.autoReply;
        
        const modal = new bootstrap.Modal(document.getElementById('editFriendModal'));
        modal.show();
    }
    
    // 更新好友信息
    function updateFriend() {
        const friendName = document.getElementById('edit-friend-id').value;
        const agent = document.getElementById('edit-friend-agent').value;
        const tags = document.getElementById('edit-friend-tags').value;
        const autoReply = document.getElementById('edit-friend-auto-reply').checked;

        const friendIndex = friendsList.findIndex(f => f.name === friendName);
        if (friendIndex === -1) return;

        // 调用后端API更新配置
        fetch(`/api/friends/${encodeURIComponent(friendName)}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                agent: agent,
                tags: tags ? tags.split(',').map(t => t.trim()) : ['好友'],
                auto_reply: autoReply
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新本地数据
                friendsList[friendIndex].agent = agent || 'default';  // 保存实际的Agent ID
                friendsList[friendIndex].tags = tags ? tags.split(',').map(t => t.trim()) : ['好友'];
                friendsList[friendIndex].autoReply = autoReply;

                renderFriendsTable();
                updateStatistics();

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('editFriendModal'));
                modal.hide();

                showNotification('好友信息已更新', 'success');
            } else {
                showNotification('更新失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('更新好友配置失败:', error);
            showNotification('更新失败: ' + error.message, 'error');
        });
    }
    
    // 删除好友
    function removeFriend(friendName) {
        if (!confirm(`确定要删除好友"${friendName}"吗？`)) {
            return;
        }
        
        fetch(`/api/friends/${encodeURIComponent(friendName)}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                friendsList = friendsList.filter(f => f.name !== friendName);
                renderFriendsTable();
                updateStatistics();
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('删除好友失败:', error);
            showNotification('删除好友失败', 'error');
        });
    }
    
    // 搜索好友
    function searchFriends() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        const filteredFriends = friendsList.filter(friend => 
            friend.name.toLowerCase().includes(searchTerm) ||
            friend.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
        
        // 临时更新显示
        const originalList = friendsList;
        friendsList = filteredFriends;
        renderFriendsTable();
        friendsList = originalList;
    }
    
    // 搜索框回车事件
    document.getElementById('search-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchFriends();
        }
    });
</script>
{% endblock %}
