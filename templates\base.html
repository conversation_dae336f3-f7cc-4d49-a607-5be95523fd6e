<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}微信智能机器人{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .card-hover {
            transition: transform 0.2s;
        }
        .card-hover:hover {
            transform: translateY(-2px);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-robot"></i>
                            微信智能机器人
                        </h4>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                控制面板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'setup' %}active{% endif %}" href="{{ url_for('setup') }}">
                                <i class="fas fa-cog"></i>
                                系统配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'friends' %}active{% endif %}" href="{{ url_for('friends') }}">
                                <i class="fas fa-users"></i>
                                好友管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'agents' %}active{% endif %}" href="{{ url_for('agents') }}">
                                <i class="fas fa-robot"></i>
                                Agent管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'tasks' %}active{% endif %}" href="{{ url_for('tasks') }}">
                                <i class="fas fa-tasks"></i>
                                任务管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'logs' %}active{% endif %}" href="{{ url_for('logs') }}">
                                <i class="fas fa-history"></i>
                                对话日志
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50">
                    
                    <!-- 状态指示器 -->
                    <div class="px-3">
                        <div class="text-white-50 small mb-2">系统状态</div>
                        <div class="text-white" id="bot-status">
                            <span class="status-indicator status-stopped"></span>
                            <span>未连接</span>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3 pb-2 mb-3">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Toast 通知 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="fas fa-info-circle text-primary me-2"></i>
                <strong class="me-auto">通知</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-message">
                <!-- 消息内容 -->
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Socket.IO 连接
        const socket = io();
        
        // 连接状态处理
        socket.on('connect', function() {
            console.log('WebSocket 连接成功');
            updateBotStatus();
        });
        
        socket.on('disconnect', function() {
            console.log('WebSocket 连接断开');
        });
        
        // 机器人状态变化
        socket.on('bot_status_changed', function(data) {
            updateBotStatus(true); // 强制更新状态
            showNotification(`机器人状态: ${data.status === 'running' ? '运行中' : '已停止'}`);
        });
        
        // 状态缓存和防抖
        let lastStatusUpdate = 0;
        let cachedStatus = null;
        const STATUS_CACHE_DURATION = 5000; // 5秒缓存
        const STATUS_UPDATE_DEBOUNCE = 1000; // 1秒防抖
        let statusUpdateTimeout = null;

        // 更新机器人状态显示
        function updateBotStatus(forceUpdate = false) {
            const now = Date.now();

            // 如果有缓存且未过期，且不是强制更新，直接使用缓存
            if (!forceUpdate && cachedStatus && (now - lastStatusUpdate) < STATUS_CACHE_DURATION) {
                applyStatusToUI(cachedStatus);
                return;
            }

            // 如果当前状态不是停止状态，且不是强制更新，延长缓存时间以减少闪烁
            if (!forceUpdate && cachedStatus && cachedStatus.is_running && (now - lastStatusUpdate) < STATUS_CACHE_DURATION * 2) {
                applyStatusToUI(cachedStatus);
                return;
            }

            // 防抖处理
            if (statusUpdateTimeout) {
                clearTimeout(statusUpdateTimeout);
            }

            statusUpdateTimeout = setTimeout(() => {
                fetch('/api/bot/status')
                    .then(response => response.json())
                    .then(data => {
                        // 只有状态真正改变时才更新UI
                        if (!cachedStatus || cachedStatus.is_running !== data.is_running || forceUpdate) {
                            cachedStatus = data;
                            lastStatusUpdate = now;
                            applyStatusToUI(data);
                        } else {
                            // 状态没有改变，只更新缓存时间
                            lastStatusUpdate = now;
                        }
                    })
                    .catch(error => {
                        console.error('获取状态失败:', error);
                        // 如果请求失败，使用缓存状态（如果有的话）
                        if (cachedStatus) {
                            applyStatusToUI(cachedStatus);
                        }
                    });
            }, forceUpdate ? 0 : STATUS_UPDATE_DEBOUNCE);
        }

        // 应用状态到UI
        function applyStatusToUI(data) {
            const statusElement = document.getElementById('bot-status');
            if (!statusElement) return;

            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span:last-child');

            if (!indicator || !text) return;

            // 只在状态真正改变时更新UI
            const newIndicatorClass = data.is_running ? 'status-indicator status-running' : 'status-indicator status-stopped';
            const newText = data.is_running ? '运行中' : '已停止';

            if (indicator.className !== newIndicatorClass) {
                indicator.className = newIndicatorClass;
            }
            if (text.textContent !== newText) {
                text.textContent = newText;
            }
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const toast = document.getElementById('notification-toast');
            const toastMessage = document.getElementById('toast-message');
            const toastIcon = toast.querySelector('.toast-header i');
            
            toastMessage.textContent = message;
            
            // 设置图标和颜色
            toastIcon.className = `fas me-2 ${
                type === 'success' ? 'fa-check-circle text-success' :
                type === 'error' ? 'fa-exclamation-circle text-danger' :
                type === 'warning' ? 'fa-exclamation-triangle text-warning' :
                'fa-info-circle text-primary'
            }`;
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // 页面加载完成后更新状态
        document.addEventListener('DOMContentLoaded', function() {
            updateBotStatus();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
