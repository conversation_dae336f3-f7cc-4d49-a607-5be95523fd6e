#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信智能机器人Web界面
基于Flask实现可视化管理界面
"""

import sys
import os
import json
import time
import threading
import webbrowser
from pathlib import Path
from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO, emit

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent / "CustomFunction"))
sys.path.insert(0, str(Path(__file__).parent))

from Config.Config import ConfigManager
from CustomFunction.WeChatBot import WeChatBot
from CustomFunction.LLM import create_llm_manager
import requests

app = Flask(__name__)
app.config['SECRET_KEY'] = 'wxauto-secret-key-2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
bot_instance = None
config_manager = None

class APIValidator:
    """API验证器"""

    def __init__(self):
        self.timeout = 10  # 请求超时时间

    def validate_deepseek_api(self, api_key: str, model_name: str = "deepseek-chat"):
        """验证DeepSeek API"""
        try:
            url = "https://api.deepseek.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            # 根据模型类型调整参数
            if model_name == "deepseek-reasoner":
                data = {
                    "model": model_name,
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 100,  # 推理模型需要更多tokens
                    "temperature": 0.1
                }
            else:
                data = {
                    "model": model_name,
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10,
                    "temperature": 0.1
                }

            response = requests.post(url, headers=headers, json=data, timeout=self.timeout)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return True, "API验证成功"
                else:
                    return False, "API响应格式异常"
            elif response.status_code == 401:
                return False, "API密钥无效"
            elif response.status_code == 429:
                return False, "API调用频率超限"
            else:
                return False, f"API调用失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "API请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接失败"
        except Exception as e:
            return False, f"验证失败: {str(e)}"

    def validate_openai_api(self, api_key: str, model_name: str = "gpt-3.5-turbo"):
        """验证OpenAI API"""
        try:
            url = "https://api.openai.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": model_name,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10,
                "temperature": 0.1
            }

            response = requests.post(url, headers=headers, json=data, timeout=self.timeout)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return True, "API验证成功"
                else:
                    return False, "API响应格式异常"
            elif response.status_code == 401:
                return False, "API密钥无效"
            elif response.status_code == 429:
                return False, "API调用频率超限"
            else:
                return False, f"API调用失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "API请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接失败"
        except Exception as e:
            return False, f"验证失败: {str(e)}"

    def validate_qwen_api(self, api_key: str, model_name: str = "qwen-turbo"):
        """验证通义千问API"""
        try:
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "model": model_name,
                "input": {
                    "messages": [{"role": "user", "content": "Hello"}]
                },
                "parameters": {
                    "max_tokens": 10,
                    "temperature": 0.1
                }
            }

            response = requests.post(url, headers=headers, json=data, timeout=self.timeout)

            if response.status_code == 200:
                result = response.json()
                if "output" in result:
                    return True, "API验证成功"
                else:
                    return False, "API响应格式异常"
            elif response.status_code == 401:
                return False, "API密钥无效"
            elif response.status_code == 429:
                return False, "API调用频率超限"
            else:
                return False, f"API调用失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            return False, "API请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接失败"
        except Exception as e:
            return False, f"验证失败: {str(e)}"

    def validate_api(self, provider: str, api_key: str, model_name: str):
        """统一API验证接口"""
        if not api_key or not api_key.strip():
            return False, "API密钥不能为空"

        if provider == "deepseek":
            return self.validate_deepseek_api(api_key, model_name)
        elif provider == "chatgpt":
            return self.validate_openai_api(api_key, model_name)
        elif provider == "qwen":
            return self.validate_qwen_api(api_key, model_name)
        else:
            return False, f"不支持的API提供商: {provider}"

api_validator = APIValidator()

def init_app():
    """初始化应用"""
    global config_manager
    try:
        config_manager = ConfigManager()
        print("配置管理器初始化成功")

        # 启动时验证所有LLM配置
        print("正在验证LLM配置...")
        validate_all_llm_configs()

    except Exception as e:
        print(f"配置管理器初始化失败: {e}")
        config_manager = None

def validate_all_llm_configs():
    """验证所有LLM配置"""
    if not config_manager:
        return

    try:
        llm_config = config_manager.get_llm_config()
        for model_name, config in llm_config.items():
            if config.get('enabled') and config.get('api_key'):
                print(f"验证 {model_name} 配置...")
                is_valid, message = api_validator.validate_api(
                    model_name,
                    config.get('api_key'),
                    config.get('model_name', model_name)
                )

                if not is_valid:
                    print(f"{model_name} 验证失败: {message}")
                    # 禁用无效的模型
                    config_manager.set_llm_enabled(model_name, False)
                else:
                    print(f"{model_name} 验证成功")

    except Exception as e:
        print(f"验证LLM配置失败: {e}")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """控制面板"""
    if not config_manager:
        return redirect(url_for('setup'))
    
    status = get_bot_status()
    return render_template('dashboard.html', status=status)

@app.route('/setup')
def setup():
    """配置页面"""
    return render_template('setup.html')

@app.route('/friends')
def friends():
    """好友管理页面"""
    return render_template('friends.html')

@app.route('/agents')
def agents():
    """Agent管理页面"""
    return render_template('agents.html')

@app.route('/tasks')
def tasks():
    """任务管理页面"""
    return render_template('tasks.html')

@app.route('/logs')
def logs():
    """对话日志页面"""
    return render_template('logs.html')

# API路由
@app.route('/api/bot/status')
def api_bot_status():
    """获取机器人状态"""
    status = get_bot_status()
    return jsonify(status)

@app.route('/api/bot/start', methods=['POST'])
def api_bot_start():
    """启动机器人"""
    global bot_instance
    try:
        if not config_manager:
            return jsonify({'success': False, 'message': '配置未初始化'})
        
        if bot_instance and bot_instance.is_running:
            return jsonify({'success': False, 'message': '机器人已在运行'})
        
        bot_instance = WeChatBot(config_manager)
        if bot_instance.start():
            socketio.emit('bot_status_changed', {'status': 'running'})
            return jsonify({'success': True, 'message': '机器人启动成功'})
        else:
            return jsonify({'success': False, 'message': '机器人启动失败'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'启动失败: {str(e)}'})

@app.route('/api/bot/stop', methods=['POST'])
def api_bot_stop():
    """停止机器人"""
    global bot_instance
    try:
        if bot_instance:
            bot_instance.stop()
            bot_instance = None
            socketio.emit('bot_status_changed', {'status': 'stopped'})
            return jsonify({'success': True, 'message': '机器人已停止'})
        else:
            return jsonify({'success': False, 'message': '机器人未运行'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'停止失败: {str(e)}'})

@app.route('/api/config/llm')
def api_get_llm_config():
    """获取LLM配置（用于显示，API密钥会被掩码）"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        llm_config = config_manager.get_llm_config()
        # 不再掩码API密钥，直接返回完整配置
        return jsonify({'success': True, 'data': llm_config})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/llm/edit')
def api_get_llm_config_for_edit():
    """获取LLM配置用于编辑（包含完整API密钥）"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        llm_config = config_manager.get_llm_config()
        # 不掩码API密钥，用于编辑
        return jsonify({'success': True, 'data': llm_config})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/llm', methods=['PUT'])
def api_update_llm_config():
    """更新LLM配置"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        data = request.get_json()
        model_name = data.get('model_name')
        api_key = data.get('api_key')
        enabled = data.get('enabled', True)
        selected_model = data.get('selected_model')

        if not model_name or not api_key:
            return jsonify({'success': False, 'message': '参数不完整'})

        config_manager.set_llm_api_key(model_name, api_key)
        config_manager.set_llm_enabled(model_name, enabled)

        # 如果提供了具体的模型选择，保存它
        if selected_model:
            config_manager.set_llm_model_name(model_name, selected_model)

        return jsonify({'success': True, 'message': '配置更新成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/validate-key', methods=['POST'])
def api_validate_key():
    """验证API密钥"""
    try:
        data = request.get_json()
        provider = data.get('provider')  # deepseek, chatgpt, qwen
        api_key = data.get('api_key')
        model_name = data.get('model_name', '')

        if not provider or not api_key:
            return jsonify({'success': False, 'message': '参数不完整'})

        # 如果没有指定模型名称，使用默认值
        if not model_name:
            default_models = {
                'deepseek': 'deepseek-chat',
                'chatgpt': 'gpt-3.5-turbo',
                'qwen': 'qwen-turbo'
            }
            model_name = default_models.get(provider, '')

        # 实际验证API密钥
        is_valid, message = api_validator.validate_api(provider, api_key, model_name)

        if is_valid:
            # 验证成功，保存到配置
            config_manager.set_llm_api_key(provider, api_key)
            config_manager.set_llm_model_name(provider, model_name)
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': f'验证失败: {str(e)}'})

@app.route('/api/friends')
def api_get_friends():
    """获取监听好友列表"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})
    
    try:
        friends = config_manager.get_listen_friends()
        return jsonify({'success': True, 'data': friends})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/friends', methods=['POST'])
def api_add_friend():
    """添加监听好友"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        data = request.get_json()
        friend_name = data.get('name')
        agent = data.get('agent')
        tags = data.get('tags', [])
        auto_reply = data.get('auto_reply', True)

        if not friend_name:
            return jsonify({'success': False, 'message': '好友名称不能为空'})

        # 如果指定了Agent，验证Agent是否存在
        if agent:
            agents = config_manager.get_agent_config()
            if agent not in agents:
                return jsonify({'success': False, 'message': f'指定的Agent "{agent}" 不存在'})

        config_manager.add_listen_friend(friend_name, agent, tags, auto_reply)
        return jsonify({'success': True, 'message': f'已添加好友: {friend_name}'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/friends/<friend_name>', methods=['PUT'])
def api_update_friend(friend_name):
    """更新好友配置"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        data = request.get_json()
        agent = data.get('agent')
        tags = data.get('tags')
        auto_reply = data.get('auto_reply')

        # 如果指定了Agent，验证Agent是否存在
        if agent:
            agents = config_manager.get_agent_config()
            if agent not in agents:
                return jsonify({'success': False, 'message': f'指定的Agent "{agent}" 不存在'})

        config_manager.update_friend_config(friend_name, agent, tags, auto_reply)
        return jsonify({'success': True, 'message': f'已更新好友配置: {friend_name}'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/friends/<friend_name>', methods=['DELETE'])
def api_remove_friend(friend_name):
    """删除监听好友"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        config_manager.remove_listen_friend(friend_name)
        return jsonify({'success': True, 'message': f'已删除好友: {friend_name}'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/config/models/<provider>')
def api_get_available_models(provider):
    """获取指定提供商的可用模型列表"""
    try:
        if not config_manager:
            return jsonify({'success': False, 'message': '配置未初始化'})

        models = config_manager.get_available_models(provider)
        return jsonify({'success': True, 'data': models})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/agents')
def api_get_agents():
    """获取Agent配置"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        agents = config_manager.get_agent_config()
        return jsonify({'success': True, 'data': agents})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/agents/<agent_id>')
def api_get_agent(agent_id):
    """获取单个Agent详情"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        agent = config_manager.get_agent_config(agent_id)
        if not agent:
            return jsonify({'success': False, 'message': 'Agent不存在'})

        return jsonify({'success': True, 'data': agent})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/agents', methods=['POST'])
def api_create_agent():
    """创建新Agent"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        data = request.get_json()
        agent_id = data.get('id') or f"agent_{int(time.time())}"
        agent_config = {
            'name': data.get('name', ''),
            'identity': data.get('identity', ''),
            'description': data.get('description', ''),
            'prompt': data.get('prompt', ''),
            'llm_model': data.get('llm_model', 'deepseek'),
            'temperature': data.get('temperature', 0.7),
            'max_tokens': data.get('max_tokens', 1000),
            'enabled': True
        }

        # 验证必填字段
        if not agent_config['name'] or not agent_config['prompt']:
            return jsonify({'success': False, 'message': '名称和提示词不能为空'})

        # 验证选择的模型是否可用
        selected_model = agent_config['llm_model']

        # 检查模型对应的提供商是否已启用
        model_provider_map = {
            'deepseek-chat': 'deepseek',
            'deepseek-reasoner': 'deepseek',
            'deepseek-coder': 'deepseek',
            'gpt-3.5-turbo': 'chatgpt',
            'gpt-4': 'chatgpt',
            'gpt-4-turbo': 'chatgpt',
            'qwen-turbo': 'qwen',
            'qwen-plus': 'qwen',
            'qwen-max': 'qwen'
        }

        provider = model_provider_map.get(selected_model)
        if not provider:
            return jsonify({'success': False, 'message': f'不支持的模型: {selected_model}'})

        # 直接检查提供商的配置状态
        llm_config = config_manager.get_llm_config(provider)
        if not llm_config.get('enabled', False):
            return jsonify({'success': False, 'message': f'模型提供商 {provider} 未启用'})

        # 检查API Key是否存在（不检查具体内容，避免掩码问题）
        if not llm_config.get('api_key'):
            return jsonify({'success': False, 'message': f'模型提供商 {provider} 的API Key未配置'})

        config_manager.add_agent_config(agent_id, agent_config)
        return jsonify({'success': True, 'message': 'Agent创建成功', 'agent_id': agent_id})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/agents/<agent_id>', methods=['PUT'])
def api_update_agent(agent_id):
    """更新Agent配置"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        data = request.get_json()

        # 获取现有配置
        existing_config = config_manager.get_agent_config(agent_id)
        if not existing_config:
            return jsonify({'success': False, 'message': 'Agent不存在'})

        # 更新配置
        updated_config = existing_config.copy()
        for key in ['name', 'identity', 'description', 'prompt', 'llm_model', 'temperature', 'max_tokens']:
            if key in data:
                updated_config[key] = data[key]

        # 验证必填字段
        if not updated_config.get('name') or not updated_config.get('prompt'):
            return jsonify({'success': False, 'message': '名称和提示词不能为空'})

        # 验证选择的模型是否可用
        selected_model = updated_config.get('llm_model')
        if selected_model:
            # 检查模型对应的提供商是否已启用
            model_provider_map = {
                'deepseek-chat': 'deepseek',
                'deepseek-reasoner': 'deepseek',
                'deepseek-coder': 'deepseek',
                'gpt-3.5-turbo': 'chatgpt',
                'gpt-4': 'chatgpt',
                'gpt-4-turbo': 'chatgpt',
                'qwen-turbo': 'qwen',
                'qwen-plus': 'qwen',
                'qwen-max': 'qwen'
            }

            provider = model_provider_map.get(selected_model)
            if not provider:
                return jsonify({'success': False, 'message': f'不支持的模型: {selected_model}'})

            # 直接检查提供商的配置状态
            llm_config = config_manager.get_llm_config(provider)
            if not llm_config.get('enabled', False):
                return jsonify({'success': False, 'message': f'模型提供商 {provider} 未启用'})

            # 检查API Key是否存在（不检查具体内容，避免掩码问题）
            if not llm_config.get('api_key'):
                return jsonify({'success': False, 'message': f'模型提供商 {provider} 的API Key未配置'})

        config_manager.add_agent_config(agent_id, updated_config)
        return jsonify({'success': True, 'message': 'Agent更新成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/agents/<agent_id>', methods=['DELETE'])
def api_delete_agent(agent_id):
    """删除Agent"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        # 检查是否为系统内置Agent
        protected_agents = ['default', 'friend', 'work']
        if agent_id in protected_agents:
            return jsonify({'success': False, 'message': '不能删除系统内置Agent模板'})

        # 检查Agent是否存在
        existing_config = config_manager.get_agent_config(agent_id)
        if not existing_config:
            return jsonify({'success': False, 'message': 'Agent不存在'})

        # 检查是否为当前使用的Agent
        current_agent = config_manager.get_current_agent()
        if agent_id == current_agent:
            return jsonify({'success': False, 'message': '不能删除当前使用的Agent'})

        config_manager.remove_agent_config(agent_id)
        return jsonify({'success': True, 'message': 'Agent删除成功'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

def get_bot_status():
    """获取机器人状态"""
    global bot_instance

    if not config_manager:
        return {
            'is_running': False,
            'listen_friends': [],
            'auto_reply_enabled': False,
            'current_llm': None,
            'current_agent': None,
            'enabled_models': [],
            'status_text': '配置未初始化',
            'status_class': 'danger'
        }

    try:
        is_running = bot_instance.is_running if bot_instance else False
        listen_friends = config_manager.get_listen_friends()
        enabled_models = list(config_manager.get_enabled_llm_models().keys())

        # 确定状态文本和样式
        if is_running:
            status_text = '运行中'
            status_class = 'success'
        elif not enabled_models:
            status_text = '未配置LLM模型'
            status_class = 'warning'
        elif not listen_friends:
            status_text = '未添加监听好友'
            status_class = 'warning'
        else:
            status_text = '已停止'
            status_class = 'secondary'

        status = {
            'is_running': is_running,
            'listen_friends': listen_friends,
            'auto_reply_enabled': config_manager.is_auto_reply_enabled(),
            'current_llm': config_manager.get_current_llm(),
            'current_agent': config_manager.get_current_agent(),
            'enabled_models': enabled_models,
            'status_text': status_text,
            'status_class': status_class,
            'friend_count': len(listen_friends),
            'model_count': len(enabled_models)
        }
        return status
    except Exception as e:
        return {
            'is_running': False,
            'listen_friends': [],
            'auto_reply_enabled': False,
            'current_llm': None,
            'current_agent': None,
            'enabled_models': [],
            'status_text': f'获取状态失败: {str(e)}',
            'status_class': 'danger',
            'friend_count': 0,
            'model_count': 0
        }

@socketio.on('connect')
def handle_connect():
    """WebSocket连接"""
    print('客户端已连接')
    emit('connected', {'message': '连接成功'})

@socketio.on('disconnect')
def handle_disconnect():
    """WebSocket断开连接"""
    print('客户端已断开连接')

# 日志管理API
@app.route('/api/logs', methods=['GET'])
def api_get_logs():
    """获取对话日志"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        # 获取查询参数
        friend_filter = request.args.get('friend')
        date_filter = request.args.get('date')
        type_filter = request.args.get('type')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        # 获取日志数据
        result = config_manager.get_logs(
            friend_filter=friend_filter,
            date_filter=date_filter,
            type_filter=type_filter,
            page=page,
            per_page=per_page
        )

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/logs/<log_id>', methods=['GET'])
def api_get_log_detail(log_id):
    """获取日志详情"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        log = config_manager.get_log_by_id(log_id)
        if log:
            return jsonify({'success': True, 'data': log})
        else:
            return jsonify({'success': False, 'message': '日志记录不存在'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


@app.route('/api/logs', methods=['DELETE'])
def api_clear_logs():
    """清空所有日志"""
    if not config_manager:
        return jsonify({'success': False, 'message': '配置未初始化'})

    try:
        config_manager.clear_logs()
        return jsonify({'success': True, 'message': '日志已清空'})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})


def open_browser():
    """自动打开浏览器"""
    import time
    time.sleep(1.5)  # 等待服务器启动
    webbrowser.open('http://localhost:5000')

if __name__ == '__main__':
    print("正在启动微信智能机器人Web界面...")
    
    # 初始化应用
    init_app()
    
    # 自动打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    # 启动Flask应用
    print("Web界面已启动: http://localhost:5000")
    socketio.run(app, host='0.0.0.0', port=5000, debug=False)
